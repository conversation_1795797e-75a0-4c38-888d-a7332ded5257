#!/usr/bin/env node
// ─────────────────────────────────────────────
// Script: test_websocket.js
// Purpose:
//   - Test WebSocket connection and message broadcasting
//   - Simulate real-time updates for debugging
// ─────────────────────────────────────────────

const WebSocket = require('ws');

const WS_URL = 'ws://localhost:4001';

console.log('🧪 Testing WebSocket connection and messaging...');
console.log(`📡 Connecting to: ${WS_URL}`);

const ws = new WebSocket(WS_URL);

ws.on('open', () => {
    console.log('✅ WebSocket connected successfully!');
    
    // Test sending different types of messages
    setTimeout(() => {
        console.log('📤 Sending test log message...');
        ws.send(JSON.stringify({
            type: 'log',
            sessionId: 'test-session',
            data: {
                message: 'Test log message from WebSocket test',
                level: 'info',
                timestamp: new Date().toISOString()
            }
        }));
    }, 1000);
    
    setTimeout(() => {
        console.log('📤 Sending test progress update...');
        ws.send(JSON.stringify({
            type: 'progress',
            sessionId: 'test-session',
            data: {
                originalSize: 1000000,
                encodedSize: 500000,
                originalSizeFormatted: '1.0 MB',
                encodedSizeFormatted: '500 KB',
                compressionRatio: '50.00',
                timestamp: new Date().toISOString()
            }
        }));
    }, 2000);
    
    setTimeout(() => {
        console.log('📤 Sending test completion message...');
        ws.send(JSON.stringify({
            type: 'completed',
            sessionId: 'test-session',
            data: {
                success: true,
                exitCode: 0,
                timestamp: new Date().toISOString()
            }
        }));
    }, 3000);
    
    // Close connection after tests
    setTimeout(() => {
        console.log('🔌 Closing WebSocket connection...');
        ws.close();
    }, 4000);
});

ws.on('message', (data) => {
    try {
        const message = JSON.parse(data.toString());
        console.log('📥 Received message:', message.type, message);
    } catch (error) {
        console.log('📥 Received raw data:', data.toString());
    }
});

ws.on('close', () => {
    console.log('🔌 WebSocket connection closed');
    process.exit(0);
});

ws.on('error', (error) => {
    console.error('❌ WebSocket error:', error.message);
    process.exit(1);
});

// Timeout to prevent hanging
setTimeout(() => {
    console.log('⏰ Test timeout - closing connection');
    ws.close();
    process.exit(1);
}, 10000);
