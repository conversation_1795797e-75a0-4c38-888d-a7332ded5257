# 🎬 Simplified MP4 Workflow - Complete Implementation

## ✅ **Workflow Overview**

The system has been simplified to use standard .mp4 files instead of complex HLS streaming:

1. **RTSP Capture** → `.ts` file (existing functionality)
2. **MP4 Conversion** → `ffmpeg -i video.ts -c copy name_file.mp4`
3. **S3 Upload** → Simple names without timestamps (`original.mp4`, `compressed.mp4`)
4. **Web Dashboard** → Direct MP4 playback in browsers

## 🔧 **Key Improvements**

### **1. Simple File Names**
- ✅ **No timestamps** - Files are named `original.mp4` and `compressed.mp4`
- ✅ **Space saving** - Each new conversion replaces the previous file
- ✅ **Consistent URLs** - Always the same S3 paths for easy integration

### **2. Direct MP4 Conversion**
- ✅ **Simple command:** `ffmpeg -i video.ts -c copy name_file.mp4`
- ✅ **No re-encoding** - Fast copy operation preserves quality
- ✅ **Universal compatibility** - MP4 works in all browsers

### **3. Streamlined S3 Structure**
```
s3://bucket/prefix/
├── original.mp4     # Always the latest original stream
└── compressed.mp4   # Always the latest compressed stream
```

## 📁 **New Scripts**

### **1. `convert_to_mp4_and_upload.sh`**
Main conversion script that replaces the complex HLS workflow:

```bash
# Usage
./convert_to_mp4_and_upload.sh <TS_FILE> <S3_BUCKET> [S3_PREFIX] [SESSION_ID]

# Examples
./convert_to_mp4_and_upload.sh original/stream/stream_123.ts my-bucket videos/ session123
./convert_to_mp4_and_upload.sh encoded/stream/stream_123.ts my-bucket videos/ session123
```

**Features:**
- Detects original vs compressed based on file path
- Creates `mp4/original.mp4` or `mp4/compressed.mp4`
- Uploads to S3 with simple names
- Sends WebSocket notifications
- Saves URLs for web dashboard integration

### **2. Updated Processing Scripts**
- `auto_process_to_hls.sh` → Now uses MP4 conversion
- `post_process_stream.sh` → Now uses MP4 conversion
- All scripts updated to use the new workflow

## 🌐 **Web Dashboard Changes**

### **Frontend Updates (app.js):**
- Added `loadCompressedMP4Stream()` method
- Added `/api/mp4-streams/:sessionId` endpoint support
- Added `mp4_ready` WebSocket message handling
- Simplified video loading (no HLS.js complexity)

### **Backend Updates (server.js):**
- Added `/api/mp4-streams/:sessionId` endpoint
- Reads MP4 URLs from `mp4/{sessionId}/` directory
- Returns both original and compressed stream info

### **WebSocket Integration:**
- `upload_started` - Shows live stream container
- `mp4_ready` - Shows compressed stream container with MP4

## 🎯 **Usage Examples**

### **Manual Processing:**
```bash
# Convert and upload original stream
./convert_to_mp4_and_upload.sh original/stream/stream_123.ts my-bucket videos/ session123

# Convert and upload compressed stream  
./convert_to_mp4_and_upload.sh encoded/stream/stream_123.ts my-bucket videos/ session123
```

### **Auto Processing:**
```bash
# Start auto-processor for new files
S3_BUCKET=my-bucket ./auto_process_to_hls.sh monitor
```

### **Post Processing:**
```bash
# Process completed stream
./post_process_stream.sh original/stream/stream_123.ts session123
```

## 📊 **API Endpoints**

### **Get MP4 Streams:**
```bash
GET /api/mp4-streams/:sessionId

# Response:
[
  {
    "filename": "compressed.mp4",
    "url": "https://bucket.s3.amazonaws.com/prefix/compressed.mp4",
    "type": "mp4",
    "streamType": "compressed",
    "source": "s3"
  },
  {
    "filename": "original.mp4", 
    "url": "https://bucket.s3.amazonaws.com/prefix/original.mp4",
    "type": "mp4",
    "streamType": "original",
    "source": "s3"
  }
]
```

## 🔔 **WebSocket Messages**

### **Upload Started:**
```javascript
{
  "type": "upload_started",
  "sessionId": "session123",
  "data": {"timestamp": "2025-06-17T14:00:00.000Z"}
}
```

### **MP4 Ready:**
```javascript
{
  "type": "mp4_ready",
  "sessionId": "session123", 
  "data": {
    "url": "https://bucket.s3.amazonaws.com/prefix/compressed.mp4",
    "filename": "compressed.mp4",
    "type": "compressed"
  }
}
```

## 🎬 **Container Visibility Flow**

1. **Start compression** → Both containers hidden
2. **Upload begins** → Live stream container visible (`upload_started`)
3. **MP4 ready** → Compressed stream container visible (`mp4_ready`)
4. **Video loads** → Direct MP4 playback

## ✅ **Benefits of Simplified Workflow**

### **1. Disk Space Savings**
- ✅ No timestamp-based file names
- ✅ Each conversion replaces previous files
- ✅ No accumulation of old files

### **2. Easier Management**
- ✅ Simple file structure
- ✅ Predictable S3 URLs
- ✅ No complex HLS segments to manage

### **3. Better Performance**
- ✅ Direct MP4 playback (no HLS.js overhead)
- ✅ Faster conversion (copy vs re-encode)
- ✅ Universal browser compatibility

### **4. Simplified Development**
- ✅ Standard video elements
- ✅ No HLS complexity
- ✅ Easier debugging and testing

## 🧪 **Testing**

### **Test Script:**
```bash
./test_mp4_workflow.sh
```

**Tests:**
- ✅ Original stream conversion
- ✅ Compressed stream conversion  
- ✅ S3 upload with simple names
- ✅ Web dashboard integration
- ✅ WebSocket notifications
- ✅ API endpoint functionality

### **Expected Results:**
- Files uploaded as `original.mp4` and `compressed.mp4`
- Web dashboard loads MP4 streams directly
- Container visibility works correctly
- No timestamp accumulation in S3

## 🚀 **Migration from HLS**

The system maintains backward compatibility:
- HLS endpoints still exist
- Old HLS files continue to work
- New conversions use MP4 workflow
- Gradual migration as new streams are processed

## 📋 **File Structure**

```
zmt-live/
├── mp4/                          # Local MP4 files
│   ├── original.mp4             # Latest original (replaced each time)
│   ├── compressed.mp4           # Latest compressed (replaced each time)
│   ├── original_url.txt         # S3 URL for original
│   ├── compressed_url.txt       # S3 URL for compressed
│   └── {sessionId}/             # Session-specific URLs for web dashboard
│       ├── original_url.txt
│       └── compressed_url.txt
├── convert_to_mp4_and_upload.sh # Main conversion script
├── test_mp4_workflow.sh         # Test script
└── logs/                        # Conversion logs
    └── mp4_conversion_*.log
```

## 🎉 **Ready for Production**

The simplified MP4 workflow is:
- ✅ **Fully tested** and validated
- ✅ **Backward compatible** with existing system
- ✅ **Space efficient** with no timestamp accumulation
- ✅ **Performance optimized** with direct MP4 playback
- ✅ **Developer friendly** with simple file management

**Result:** A much simpler, more efficient streaming system that saves disk space and provides better performance! 🚀
