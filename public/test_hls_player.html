<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HLS Stream Test Player</title>
    <script src="https://cdn.jsdelivr.net/npm/hls.js@latest"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #1b1b1b;
            color: #e2e8f0;
            margin: 0;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        h1 {
            color: #FC9546;
            text-align: center;
        }
        
        .test-section {
            background: #2d2d2d;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            border: 1px solid rgba(252, 149, 70, 0.2);
        }
        
        .input-group {
            margin: 15px 0;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            color: #FC9546;
            font-weight: bold;
        }
        
        input[type="text"] {
            width: 100%;
            padding: 10px;
            background: #1b1b1b;
            border: 1px solid #639884;
            border-radius: 4px;
            color: #e2e8f0;
            font-size: 14px;
        }
        
        button {
            background: #FC9546;
            color: #1b1b1b;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
            margin: 5px;
        }
        
        button:hover {
            background: #e8843d;
        }
        
        button:disabled {
            background: #666;
            cursor: not-allowed;
        }
        
        .video-container {
            position: relative;
            background: #000;
            border-radius: 8px;
            overflow: hidden;
            margin: 20px 0;
        }
        
        video {
            width: 100%;
            height: auto;
            display: block;
        }
        
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-weight: bold;
        }
        
        .status.success {
            background: rgba(99, 152, 132, 0.2);
            border: 1px solid #639884;
            color: #639884;
        }
        
        .status.error {
            background: rgba(252, 129, 129, 0.2);
            border: 1px solid #fc8181;
            color: #fc8181;
        }
        
        .status.info {
            background: rgba(252, 149, 70, 0.2);
            border: 1px solid #FC9546;
            color: #FC9546;
        }
        
        .log {
            background: #1b1b1b;
            border: 1px solid #444;
            border-radius: 4px;
            padding: 15px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        
        .test-urls {
            background: #2d2d2d;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        
        .test-urls h4 {
            color: #FC9546;
            margin-top: 0;
        }
        
        .url-item {
            margin: 8px 0;
            padding: 8px;
            background: #1b1b1b;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎬 HLS Stream Test Player</h1>
        
        <div class="test-section">
            <h3>Test HLS Playlist Streaming</h3>
            
            <div class="input-group">
                <label for="hlsUrl">HLS Playlist URL (.m3u8):</label>
                <input type="text" id="hlsUrl" placeholder="https://your-bucket.s3.amazonaws.com/streams/stream_name/playlist.m3u8">
            </div>
            
            <button onclick="loadHLSStream()">Load HLS Stream</button>
            <button onclick="clearStream()">Clear Stream</button>
            <button onclick="testPlaylist()">Test Playlist</button>
            
            <div id="status"></div>
            
            <div class="video-container">
                <video id="video" controls muted>
                    Your browser does not support the video tag.
                </video>
            </div>
            
            <div class="test-urls">
                <h4>Sample Test URLs:</h4>
                <div class="url-item">
                    <strong>Apple Test Stream:</strong><br>
                    https://devstreaming-cdn.apple.com/videos/streaming/examples/img_bipbop_adv_example_fmp4/master.m3u8
                </div>
                <div class="url-item">
                    <strong>Big Buck Bunny:</strong><br>
                    https://test-streams.mux.dev/x36xhzz/x36xhzz.m3u8
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>Debug Information</h3>
            <div id="log" class="log">Ready to test HLS streaming...\n</div>
        </div>
    </div>

    <script>
        let hls = null;
        const video = document.getElementById('video');
        const hlsUrlInput = document.getElementById('hlsUrl');
        const statusDiv = document.getElementById('status');
        const logDiv = document.getElementById('log');

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            logDiv.textContent += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
            
            if (type === 'error') {
                console.error(message);
            } else {
                console.log(message);
            }
        }

        function showStatus(message, type = 'info') {
            statusDiv.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        function loadHLSStream() {
            const url = hlsUrlInput.value.trim();
            
            if (!url) {
                showStatus('Please enter an HLS URL', 'error');
                return;
            }
            
            if (!url.includes('.m3u8')) {
                showStatus('URL must point to a .m3u8 playlist file', 'error');
                return;
            }
            
            log(`Loading HLS stream: ${url}`);
            showStatus('Loading HLS stream...', 'info');
            
            // Clean up existing HLS instance
            if (hls) {
                hls.destroy();
                hls = null;
            }
            
            if (Hls.isSupported()) {
                log('Using HLS.js for playback');
                
                hls = new Hls({
                    enableWorker: true,
                    lowLatencyMode: false,
                    backBufferLength: 90,
                    debug: false
                });
                
                hls.loadSource(url);
                hls.attachMedia(video);
                
                hls.on(Hls.Events.MANIFEST_PARSED, function() {
                    log('✅ HLS manifest parsed successfully');
                    showStatus('HLS stream loaded successfully!', 'success');
                    
                    // Try to play
                    video.play().catch(e => {
                        log('Autoplay prevented - click play button');
                        showStatus('Stream ready - click play button', 'success');
                    });
                });
                
                hls.on(Hls.Events.ERROR, function(event, data) {
                    log(`❌ HLS Error: ${data.type} - ${data.details}`, 'error');
                    
                    if (data.fatal) {
                        switch(data.type) {
                            case Hls.ErrorTypes.NETWORK_ERROR:
                                showStatus('Network error - check URL and CORS settings', 'error');
                                break;
                            case Hls.ErrorTypes.MEDIA_ERROR:
                                showStatus('Media error - invalid stream format', 'error');
                                break;
                            default:
                                showStatus(`Fatal error: ${data.details}`, 'error');
                                break;
                        }
                    }
                });
                
                hls.on(Hls.Events.LEVEL_LOADED, function(event, data) {
                    log(`Level loaded: ${data.details} (${data.stats.total} bytes)`);
                });
                
            } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
                log('Using native HLS support (Safari)');
                video.src = url;
                
                video.addEventListener('loadedmetadata', function() {
                    log('✅ Native HLS stream loaded');
                    showStatus('HLS stream loaded successfully!', 'success');
                });
                
                video.addEventListener('error', function(e) {
                    log(`❌ Native HLS error: ${e.message}`, 'error');
                    showStatus('Failed to load HLS stream', 'error');
                });
                
            } else {
                log('❌ HLS not supported in this browser', 'error');
                showStatus('HLS not supported in this browser', 'error');
            }
        }

        function clearStream() {
            if (hls) {
                hls.destroy();
                hls = null;
            }
            
            video.src = '';
            video.load();
            
            showStatus('Stream cleared', 'info');
            log('Stream cleared');
        }

        async function testPlaylist() {
            const url = hlsUrlInput.value.trim();
            
            if (!url) {
                showStatus('Please enter an HLS URL', 'error');
                return;
            }
            
            log(`Testing playlist accessibility: ${url}`);
            showStatus('Testing playlist...', 'info');
            
            try {
                const response = await fetch(url, { method: 'HEAD' });
                
                if (response.ok) {
                    log('✅ Playlist is accessible via HTTP');
                    
                    // Test content
                    const contentResponse = await fetch(url);
                    const content = await contentResponse.text();
                    
                    if (content.includes('#EXTM3U')) {
                        log('✅ Valid HLS playlist content detected');
                        
                        const segments = (content.match(/\.ts/g) || []).length;
                        log(`Found ${segments} segments in playlist`);
                        
                        if (segments > 0) {
                            showStatus(`✅ Valid playlist with ${segments} segments`, 'success');
                        } else {
                            showStatus('⚠️ Playlist has no segments', 'error');
                        }
                    } else {
                        log('❌ Invalid playlist content');
                        showStatus('Invalid playlist content', 'error');
                    }
                } else {
                    log(`❌ Playlist not accessible: ${response.status} ${response.statusText}`, 'error');
                    showStatus(`Playlist not accessible: ${response.status}`, 'error');
                }
            } catch (error) {
                log(`❌ Network error: ${error.message}`, 'error');
                showStatus(`Network error: ${error.message}`, 'error');
            }
        }

        // Load sample URL on page load
        window.addEventListener('load', function() {
            hlsUrlInput.value = 'https://test-streams.mux.dev/x36xhzz/x36xhzz.m3u8';
            log('HLS Test Player ready');
            log('Enter an HLS URL and click "Load HLS Stream" to test');
        });
    </script>
</body>
</html>
