class RTSPDashboard {
    constructor() {
        this.ws = null;
        this.currentSessionId = null;
        this.startTime = null;
        this.estimatedDuration = 0;
        this.progressInterval = null;
        this.compressedStreamLoaded = false;
        this.liveStreamShown = false;

        this.initializeElements();
        this.bindEvents();
        this.connectWebSocket();
        this.initializeS3FilePicker();
    }

    initializeElements() {
        // Form elements
        this.form = document.getElementById('compressionForm');
        this.rtspUrlInput = document.getElementById('rtspUrl');
        this.durationInput = document.getElementById('duration');
        this.startBtn = document.getElementById('startBtn');
        this.stopBtn = document.getElementById('stopBtn');

        // Status elements
        this.currentStatus = document.getElementById('currentStatus');
        this.sessionIdDisplay = document.getElementById('sessionId');
        this.configuredDuration = document.getElementById('configuredDuration');

        // Metrics elements
        this.rawSize = document.getElementById('rawSize');
        this.compressedSize = document.getElementById('compressedSize');
        this.compressionRatio = document.getElementById('compressionRatio');
        this.bandwidthSavings = document.getElementById('bandwidthSavings');

        // Progress elements
        this.progressSection = document.querySelector('.progress-section');
        this.progressFill = document.getElementById('progressFill');
        this.progressPercent = document.getElementById('progressPercent');
        this.timeRemaining = document.getElementById('timeRemaining');

        // Logs and report
        this.logsContainer = document.getElementById('logsContainer');
        this.reportSection = document.querySelector('.report-section');
        this.finalReport = document.getElementById('finalReport');

        // Stream elements
        this.streamSection = document.querySelector('.stream-section');
        this.liveStream = document.getElementById('liveStream');
        this.compressedStream = document.getElementById('compressedStream');
        this.liveStreamOverlay = document.getElementById('liveStreamOverlay');
        this.compressedStreamOverlay = document.getElementById('compressedStreamOverlay');

        // S3 File Picker elements
        this.browseOriginalS3 = document.getElementById('browseOriginalS3');
        this.browseCompressedS3 = document.getElementById('browseCompressedS3');
        this.s3FileModal = document.getElementById('s3FileModal');
        this.s3FileList = document.getElementById('s3FileList');
        this.closeS3Modal = document.getElementById('closeS3Modal');
        this.cancelS3Selection = document.getElementById('cancelS3Selection');
        this.confirmS3Selection = document.getElementById('confirmS3Selection');

        // File info display elements
        this.originalFileInfo = document.getElementById('originalFileInfo');
        this.originalFileName = document.getElementById('originalFileName');
        this.originalFileSize = document.getElementById('originalFileSize');
        this.originalFileSource = document.getElementById('originalFileSource');
        this.compressedFileInfo = document.getElementById('compressedFileInfo');
        this.compressedFileName = document.getElementById('compressedFileName');
        this.compressedFileSize = document.getElementById('compressedFileSize');
        this.compressedFileSource = document.getElementById('compressedFileSource');

        // S3 picker state
        this.currentS3Files = [];
        this.selectedS3File = null;
        this.currentS3StreamType = null; // 'original' or 'compressed'
    }

    bindEvents() {
        this.form.addEventListener('submit', (e) => this.handleStartCompression(e));
        this.stopBtn.addEventListener('click', () => this.handleStopCompression());

        // S3 File Picker events
        this.browseOriginalS3.addEventListener('click', () => this.openS3FilePicker('original'));
        this.browseCompressedS3.addEventListener('click', () => this.openS3FilePicker('compressed'));
        this.closeS3Modal.addEventListener('click', () => this.closeS3FilePicker());
        this.cancelS3Selection.addEventListener('click', () => this.closeS3FilePicker());
        this.confirmS3Selection.addEventListener('click', () => this.loadSelectedS3File());

        // Close modal when clicking outside
        this.s3FileModal.addEventListener('click', (e) => {
            if (e.target === this.s3FileModal) {
                this.closeS3FilePicker();
            }
        });
    }

    connectWebSocket() {
        const wsUrl = `ws://${window.location.hostname}:4001`;
        this.ws = new WebSocket(wsUrl);

        this.ws.onopen = () => {
            this.addLog('WebSocket connected', 'info');
        };

        this.ws.onmessage = (event) => {
            const message = JSON.parse(event.data);
            this.handleWebSocketMessage(message);
        };

        this.ws.onclose = () => {
            this.addLog('WebSocket disconnected. Attempting to reconnect...', 'warning');
            setTimeout(() => this.connectWebSocket(), 3000);
        };

        this.ws.onerror = (error) => {
            this.addLog('WebSocket error occurred', 'error');
        };
    }

    handleWebSocketMessage(message) {
        console.log('WebSocket message received:', message.type, message);

        switch (message.type) {
            case 'connection':
                this.addLog('WebSocket connection confirmed', 'success');
                break;
            case 'progress':
                this.updateMetrics(message.data);
                break;
            case 'completed':
                this.handleProcessCompleted(message.data);
                break;
            case 'error':
                this.handleProcessError(message.data);
                break;
            case 'log':
                this.addLog(message.data.message, message.data.level || 'info');
                break;
            case 'upload_started':
                this.handleUploadStarted(message.data);
                break;
            case 'hls_ready':
                this.handleHLSReady(message.data);
                break;
            case 'mp4_ready':
                this.handleMP4Ready(message.data);
                break;
            default:
                console.log('Unknown WebSocket message type:', message.type);
        }
    }

    handleUploadStarted(data) {
        // Show live stream container when upload process begins
        this.showLiveStreamContainer();
        this.addLog('Upload process started - live stream viewer now visible', 'info');
    }

    handleHLSReady(data) {
        // This will be called when HLS conversion and upload is complete
        this.addLog('HLS streaming ready', 'success');
    }

    handleMP4Ready(data) {
        // This will be called when MP4 conversion and upload is complete
        this.addLog(`MP4 streaming ready: ${data.type}`, 'success');

        // Load the appropriate stream based on type
        if (data.type === 'compressed' && data.url) {
            this.loadCompressedMP4Stream(data.url, data.filename);
        } else if (data.type === 'original' && data.url) {
            if (!this.liveStreamShown) {
                this.showLiveStreamContainer();
                this.liveStreamShown = true;
            }
            this.loadOriginalMP4Stream(data.url, data.filename);
        }
    }

    async handleStartCompression(e) {
        e.preventDefault();

        const rtspUrl = this.rtspUrlInput.value.trim();
        const durationMinutes = parseFloat(this.durationInput.value);

        if (!rtspUrl || !durationMinutes) {
            this.addLog('Please enter both RTSP URL and duration', 'error');
            return;
        }

        try {
            this.setUIState('starting');

            // Clean up old files and show live stream section immediately
            await this.cleanupOldFiles();
            this.showLiveStreamSection();
            this.setupLiveStream();

            const response = await fetch('/api/start-compression', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    rtspUrl,
                    durationMinutes
                })
            });

            const result = await response.json();

            if (result.success) {
                this.currentSessionId = result.sessionId;
                this.startTime = new Date();
                this.estimatedDuration = durationMinutes * 60 * 1000; // Convert to milliseconds

                this.setUIState('running');
                this.updateStatus('Running', result.sessionId, result.duration);
                this.addLog(`Compression started - Session: ${result.sessionId}`, 'info');
                this.addLog(`Duration: ${result.duration}`, 'info');
                this.addLog(`Live stream preview started`, 'info');

                this.startProgressTracking();
            } else {
                throw new Error(result.error || 'Failed to start compression');
            }
        } catch (error) {
            this.addLog(`Error: ${error.message}`, 'error');
            this.setUIState('ready');
        }
    }

    async handleStopCompression() {
        if (!this.currentSessionId) return;

        try {
            const response = await fetch(`/api/stop-compression/${this.currentSessionId}`, {
                method: 'POST'
            });

            const result = await response.json();
            
            if (result.success) {
                this.addLog('Process stopped by user', 'warning');
                this.setUIState('ready');
                this.stopProgressTracking();
            }
        } catch (error) {
            this.addLog(`Error stopping process: ${error.message}`, 'error');
        }
    }

    updateMetrics(data) {
        this.rawSize.textContent = data.originalSizeFormatted;
        this.compressedSize.textContent = data.encodedSizeFormatted;
        this.compressionRatio.textContent = `${data.compressionRatio}%`;
        this.bandwidthSavings.textContent = `${data.compressionRatio}%`;

        // Update stream availability
        this.updateStreamAvailability(data);
    }

    updateStreamAvailability(data) {
        // Check if we have compressed data
        if (data.encodedSize > 0 && this.compressedStreamOverlay.innerHTML.includes('Waiting for')) {
            this.showCompressedStreamReady();
        }

        // Check if we have original data
        if (data.originalSize > 0 && this.liveStreamOverlay.innerHTML.includes('Connecting to')) {
            this.showLiveStreamReady();
        }
    }

    handleProcessCompleted(data) {
        this.addLog(`Process completed with exit code: ${data.exitCode}`, data.success ? 'info' : 'error');
        this.setUIState('completed');
        this.stopProgressTracking();

        if (data.success) {
            // Handle S3 transition for compressed stream
            this.handleS3Transition();
            this.generateFinalReport();
        }
    }

    handleProcessError(data) {
        this.addLog(`Process error: ${data.error}`, 'error');
        this.setUIState('ready');
        this.stopProgressTracking();
    }

    setUIState(state) {
        switch (state) {
            case 'ready':
                this.startBtn.style.display = 'inline-flex';
                this.stopBtn.style.display = 'none';
                this.startBtn.disabled = false;
                this.progressSection.style.display = 'none';
                this.streamSection.style.display = 'none';
                this.currentSessionId = null;
                this.updateStatus('Ready', '-', '-');
                this.hideStreams();
                break;
            case 'starting':
                this.startBtn.disabled = true;
                this.updateStatus('Starting...', '-', '-');
                break;
            case 'running':
                this.startBtn.style.display = 'none';
                this.stopBtn.style.display = 'inline-flex';
                this.progressSection.style.display = 'block';
                this.streamSection.style.display = 'block';
                this.updateStatus('Running', this.currentSessionId, this.configuredDuration.textContent);
                this.initializeStreams();
                break;
            case 'completed':
                this.startBtn.style.display = 'inline-flex';
                this.stopBtn.style.display = 'none';
                this.startBtn.disabled = false;
                this.updateStatus('Completed', this.currentSessionId, this.configuredDuration.textContent);
                break;
        }
    }

    updateStatus(status, sessionId, duration) {
        this.currentStatus.textContent = status;
        this.sessionIdDisplay.textContent = sessionId;
        this.configuredDuration.textContent = duration;
    }

    startProgressTracking() {
        this.progressInterval = setInterval(() => {
            if (!this.startTime || !this.estimatedDuration) return;

            const elapsed = Date.now() - this.startTime.getTime();
            const progress = Math.min((elapsed / this.estimatedDuration) * 100, 100);
            const remaining = Math.max(this.estimatedDuration - elapsed, 0);

            this.progressFill.style.width = `${progress}%`;
            this.progressPercent.textContent = `${Math.round(progress)}%`;
            
            if (remaining > 0) {
                const minutes = Math.floor(remaining / 60000);
                const seconds = Math.floor((remaining % 60000) / 1000);
                this.timeRemaining.textContent = `${minutes}:${seconds.toString().padStart(2, '0')} remaining`;
            } else {
                this.timeRemaining.textContent = 'Finalizing...';
            }
        }, 1000);
    }

    stopProgressTracking() {
        if (this.progressInterval) {
            clearInterval(this.progressInterval);
            this.progressInterval = null;
        }
    }

    addLog(message, level = 'info') {
        const logEntry = document.createElement('div');
        logEntry.className = `log-entry ${level}`;
        
        const timestamp = new Date().toLocaleTimeString();
        logEntry.innerHTML = `
            <span class="log-time">[${timestamp}]</span>
            <span class="log-message">${message}</span>
        `;
        
        this.logsContainer.appendChild(logEntry);
        this.logsContainer.scrollTop = this.logsContainer.scrollHeight;
    }

    showLiveStreamSection() {
        // Show the stream section immediately when compression starts
        this.streamSection.style.display = 'block';
        this.streamSection.classList.add('fade-in');

        // Reset stream states for new session
        this.liveStreamShown = false;
        this.compressedStreamLoaded = false;

        // Clear any existing video sources to prioritize live stream
        this.liveStream.src = '';
        this.compressedStream.src = '';

        // Show only the live stream container immediately
        this.showLiveStreamContainer();

        // Hide compressed stream container until it's ready
        const compressedStreamBox = document.querySelector('.stream-box:last-child');
        if (compressedStreamBox) {
            compressedStreamBox.style.display = 'none';
        }

        this.addLog('Live stream section displayed - prioritizing live RTSP feed', 'info');
    }

    async cleanupOldFiles() {
        // Remove old MP4 files to ensure fresh start for new session
        try {
            await fetch('/api/cleanup-mp4', { method: 'POST' });
            this.addLog('Cleaned up old MP4 files for fresh session', 'info');
        } catch (error) {
            console.log('Cleanup not available, continuing with session');
        }
    }

    initializeStreams() {
        // Show live stream section but hide video containers initially
        this.streamSection.style.display = 'block';
        this.streamSection.classList.add('fade-in');

        // Hide video containers until HLS streams are ready
        this.hideVideoContainers();

        // Set up live RTSP stream display (but keep hidden)
        this.setupLiveStream();

        // Monitor for compressed stream availability
        this.monitorCompressedStream();
    }

    hideVideoContainers() {
        // Hide both video containers initially
        const liveStreamBox = document.querySelector('.stream-box:first-child');
        const compressedStreamBox = document.querySelector('.stream-box:last-child');

        if (liveStreamBox) {
            liveStreamBox.style.display = 'none';
        }
        if (compressedStreamBox) {
            compressedStreamBox.style.display = 'none';
        }

        this.addLog('Stream viewers hidden until processing begins', 'info');
    }

    showLiveStreamContainer() {
        // Show only the live stream container when upload begins
        const liveStreamBox = document.querySelector('.stream-box:first-child');

        if (liveStreamBox) {
            liveStreamBox.style.display = 'block';
            liveStreamBox.classList.add('fade-in');
        }

        this.addLog('Live stream viewer now visible - upload in progress', 'info');
    }

    showCompressedStreamContainer() {
        // Show the compressed stream container when HLS is ready
        const compressedStreamBox = document.querySelector('.stream-box:last-child');

        if (compressedStreamBox) {
            compressedStreamBox.style.display = 'block';
            compressedStreamBox.classList.add('fade-in');
        }

        this.addLog('Compressed stream viewer now visible - HLS ready', 'success');
    }

    showVideoContainers() {
        // Show both video containers (fallback method)
        this.showLiveStreamContainer();
        this.showCompressedStreamContainer();
    }

    setupLiveStream() {
        // Get the RTSP URL from the form
        const rtspUrl = this.rtspUrlInput.value.trim();

        // Show live stream info with connecting status
        this.liveStreamOverlay.innerHTML = `
            <i class="fas fa-spinner fa-spin"></i>
            <p>🔴 LIVE: Connecting to RTSP Stream</p>
            <small style="color: #639884; margin-top: 5px;">${rtspUrl}</small>
        `;

        // Try to set up HLS stream for the live feed
        this.setupHLSStream(rtspUrl);
    }

    async setupHLSStream(rtspUrl) {
        try {
            // Request HLS conversion from backend
            const response = await fetch('/api/hls/start', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ rtspUrl, sessionId: this.currentSessionId })
            });

            if (response.ok) {
                const result = await response.json();
                if (result.hlsUrl) {
                    this.loadHLSStream(result.hlsUrl);
                }
            } else {
                this.showLiveStreamFallback();
            }
        } catch (error) {
            console.log('HLS conversion not available, showing status only');
            this.showLiveStreamFallback();
        }
    }

    loadHLSStream(hlsUrl) {
        if (Hls.isSupported()) {
            // Use HLS.js for browsers that don't support HLS natively
            this.hls = new Hls({
                enableWorker: true,
                lowLatencyMode: true,
                backBufferLength: 90
            });

            this.hls.loadSource(hlsUrl);
            this.hls.attachMedia(this.liveStream);

            this.hls.on(Hls.Events.MANIFEST_PARSED, () => {
                this.liveStreamOverlay.innerHTML = `
                    <i class="fas fa-play-circle"></i>
                    <p>🔴 LIVE: Stream Ready</p>
                    <small style="color: #639884; margin-top: 5px;">Real-time RTSP feed</small>
                `;
                setTimeout(() => {
                    this.liveStreamOverlay.classList.add('hidden');
                    this.liveStream.play().catch(e => console.log('Autoplay prevented'));
                }, 1000);
                this.addLog('Live HLS stream loaded', 'info');
            });

            this.hls.on(Hls.Events.ERROR, (event, data) => {
                console.error('HLS error:', data);
                this.showLiveStreamFallback();
            });
        } else if (this.liveStream.canPlayType('application/vnd.apple.mpegurl')) {
            // Native HLS support (Safari)
            this.liveStream.src = hlsUrl;
            this.liveStream.addEventListener('loadedmetadata', () => {
                this.liveStreamOverlay.classList.add('hidden');
                this.liveStream.play().catch(e => console.log('Autoplay prevented'));
                this.addLog('Live HLS stream loaded (native)', 'info');
            });
        } else {
            this.showLiveStreamFallback();
        }
    }



    showLiveStreamFallback() {
        this.liveStreamOverlay.innerHTML = `
            <i class="fas fa-video"></i>
            <p>RTSP Stream Active</p>
            <small style="color: #639884; margin-top: 5px;">Live capture in progress</small>
        `;
    }

    showLiveStreamReady() {
        this.liveStreamOverlay.innerHTML = `
            <i class="fas fa-play-circle"></i>
            <p>Raw stream being captured</p>
            <small style="color: #639884; margin-top: 5px;">Live data available</small>
        `;
    }

    monitorCompressedStream() {
        // Start checking for compressed file availability
        this.compressedStreamCheckInterval = setInterval(() => {
            if (this.currentSessionId) {
                this.checkCompressedStreamAvailability();
                this.checkLocalMP4Files();
            }
        }, 2000);

        // Also listen for process completion to handle S3 transition
        this.setupS3TransitionHandling();
    }

    async checkLocalMP4Files() {
        // Check if local MP4 files exist and show stream viewers accordingly
        // Skip if we're in the middle of starting a new compression (prioritize live stream)
        if (!this.currentSessionId) {
            return; // No active session, don't load old files
        }

        try {
            // Check for original MP4 - only load if live stream isn't already being shown
            const originalResponse = await fetch('/mp4/original.mp4', { method: 'HEAD' });
            if (originalResponse.ok && !this.liveStreamShown && !this.liveStream.src) {
                this.showLiveStreamContainer();
                this.loadOriginalMP4Stream('/mp4/original.mp4', 'original.mp4');
                this.liveStreamShown = true;
                this.addLog('Original MP4 file detected - showing live stream viewer with video', 'info');
            }

            // Check for compressed MP4
            const compressedResponse = await fetch('/mp4/compressed.mp4', { method: 'HEAD' });
            if (compressedResponse.ok && !this.compressedStreamLoaded) {
                // Load the compressed MP4 directly
                this.loadCompressedMP4Stream('/mp4/compressed.mp4', 'compressed.mp4');
                this.addLog('Compressed MP4 file detected - loading stream', 'success');
            }
        } catch (error) {
            // Files don't exist yet, continue monitoring
        }
    }

    async checkCompressedStreamAvailability() {
        if (!this.currentSessionId) return;

        try {
            // Check for MP4 streams first (preferred for web streaming)
            const mp4Response = await fetch(`/api/mp4-streams/${this.currentSessionId}`);
            if (mp4Response.ok) {
                const mp4Streams = await mp4Response.json();
                if (mp4Streams.length > 0 && !this.compressedStreamLoaded) {
                    const latestMP4 = mp4Streams[mp4Streams.length - 1];
                    this.loadCompressedMP4Stream(latestMP4.url, latestMP4.filename);
                    return;
                }
            }

            // Fallback to regular streams API for local files
            const response = await fetch(`/api/streams/${this.currentSessionId}`);
            if (response.ok) {
                const streams = await response.json();

                // Check if we have any encoded files locally
                if (streams.encoded && streams.encoded.length > 0) {
                    const encodedFile = streams.encoded[0]; // Get the first/latest encoded file
                    if (!this.compressedStreamLoaded) {
                        this.loadCompressedStream(encodedFile.url, encodedFile.filename, 'local');
                    }
                    return;
                }
            }
        } catch (error) {
            console.log('Error checking stream availability:', error);
        }
    }

    setupS3TransitionHandling() {
        // This will be called when the process completes and files move to S3
        // We'll handle this in the WebSocket message handler
    }

    showCompressedStreamReady() {
        this.compressedStreamOverlay.innerHTML = `
            <i class="fas fa-cog fa-spin"></i>
            <p>Compression in progress</p>
            <small style="color: #639884; margin-top: 5px;">Generating compressed stream...</small>
        `;

        // Start monitoring for the actual file
        if (!this.compressedStreamCheckInterval) {
            this.monitorCompressedStream();
        }
    }

    loadOriginalMP4Stream(mp4Url, filename) {
        // Load the original MP4 stream into the live stream video element
        this.liveStream.src = mp4Url;

        // Update file info for local files (S3 files are handled in loadSelectedS3File)
        if (mp4Url.startsWith('/mp4/')) {
            // This is a local file, try to get size
            this.updateLocalFileInfo('original', filename, mp4Url);
        }

        // Update overlay to show it's ready
        this.liveStreamOverlay.innerHTML = `
            <i class="fas fa-play-circle"></i>
            <p>Original Stream Ready</p>
            <small style="color: #639884; margin-top: 5px;">${filename}</small>
        `;

        // Hide overlay after a short delay and start video
        setTimeout(() => {
            this.liveStreamOverlay.classList.add('hidden');
            this.liveStream.play().catch(e => {
                console.log('Autoplay prevented for original stream');
                this.addLog('Original stream ready - click to play', 'info');
            });
        }, 1000);

        this.addLog(`Original MP4 stream loaded: ${filename}`, 'success');
    }

    loadCompressedMP4Stream(mp4Url, filename) {
        this.compressedStreamLoaded = true;

        // Show only the compressed stream container now that MP4 is ready
        this.showCompressedStreamContainer();

        // Update file info for local files (S3 files are handled in loadSelectedS3File)
        if (mp4Url.startsWith('/mp4/')) {
            // This is a local file, try to get size
            this.updateLocalFileInfo('compressed', filename, mp4Url);
        }

        this.compressedStreamOverlay.innerHTML = `
            <i class="fas fa-cloud-download-alt"></i>
            <p>Loading MP4 Stream</p>
            <small style="color: #639884; margin-top: 5px;">MP4: ${filename}</small>
        `;

        // Validate MP4 URL format
        if (!mp4Url.includes('.mp4')) {
            console.error('Invalid MP4 URL - missing .mp4:', mp4Url);
            this.compressedStreamOverlay.innerHTML = `
                <i class="fas fa-exclamation-triangle"></i>
                <p>Invalid MP4 URL</p>
                <small style="color: #fc8181; margin-top: 5px;">URL must point to .mp4 file</small>
            `;
            return;
        }

        this.addLog(`Loading MP4 video: ${mp4Url}`, 'info');

        // Clean up any existing HLS instance
        if (this.compressedHls) {
            this.compressedHls.destroy();
            this.compressedHls = null;
        }

        // Load MP4 directly into video element
        this.compressedStream.src = mp4Url;

        this.compressedStream.addEventListener('loadedmetadata', () => {
            this.compressedStreamOverlay.innerHTML = `
                <i class="fas fa-play-circle"></i>
                <p>MP4 Stream Ready</p>
                <small style="color: #639884; margin-top: 5px;">Video loaded successfully</small>
            `;

            setTimeout(() => {
                this.compressedStreamOverlay.classList.add('hidden');
                this.compressedStream.play().catch(e => console.log('Autoplay prevented'));
            }, 1500);

            this.addLog(`✅ MP4 video loaded successfully: ${filename}`, 'success');

            // Stop monitoring since we have the MP4 stream
            if (this.compressedStreamCheckInterval) {
                clearInterval(this.compressedStreamCheckInterval);
                this.compressedStreamCheckInterval = null;
            }
        });

        this.compressedStream.addEventListener('error', (e) => {
            console.error('MP4 loading error:', e);
            this.compressedStreamOverlay.innerHTML = `
                <i class="fas fa-exclamation-triangle"></i>
                <p>MP4 Stream Error</p>
                <small style="color: #fc8181; margin-top: 5px;">Failed to load video</small>
            `;
            this.addLog(`❌ MP4 error: Failed to load video`, 'error');
        });
    }

    loadCompressedHLSStream(hlsUrl, filename) {
        // Fallback method for HLS - redirect to MP4 loading
        this.addLog('HLS method called - redirecting to MP4 loading', 'info');
        this.loadCompressedMP4Stream(hlsUrl, filename);
    }

    loadCompressedStream(streamUrl, filename, source = 'local') {
        this.compressedStreamLoaded = true;
        this.compressedStream.src = streamUrl;

        const sourceLabel = source === 'local' ? 'Local file' : 'S3 file';
        this.compressedStreamOverlay.innerHTML = `
            <i class="fas fa-play-circle"></i>
            <p>Compressed Stream Ready</p>
            <small style="color: #639884; margin-top: 5px;">${sourceLabel}: ${filename}</small>
        `;

        // Set up video event handlers
        this.compressedStream.addEventListener('loadedmetadata', () => {
            this.compressedStreamOverlay.classList.add('hidden');
            this.addLog(`Compressed stream loaded from ${source}: ${filename}`, 'info');
        });

        this.compressedStream.addEventListener('error', (e) => {
            console.error('Video loading error:', e);
            this.compressedStreamOverlay.innerHTML = `
                <i class="fas fa-exclamation-triangle"></i>
                <p>Stream Error</p>
                <small style="color: #fc8181; margin-top: 5px;">Failed to load video</small>
            `;
        });

        // If this is a local file and we're still processing, keep monitoring for S3 transition
        if (source === 'local') {
            // Don't clear the interval yet - we want to detect S3 transition
            this.addLog(`Playing local compressed file: ${filename}`, 'info');
        } else {
            // This is from S3, we can stop monitoring
            if (this.compressedStreamCheckInterval) {
                clearInterval(this.compressedStreamCheckInterval);
                this.compressedStreamCheckInterval = null;
            }
            this.addLog(`Switched to S3 file: ${filename}`, 'info');
        }
    }

    handleS3Transition() {
        // Called when files are moved to S3
        if (this.compressedStreamLoaded) {
            this.compressedStreamOverlay.innerHTML = `
                <i class="fas fa-cloud-upload-alt"></i>
                <p>Files Uploaded to S3</p>
                <small style="color: #639884; margin-top: 5px;">Compression complete</small>
            `;

            // Clear monitoring since files are now in S3
            if (this.compressedStreamCheckInterval) {
                clearInterval(this.compressedStreamCheckInterval);
                this.compressedStreamCheckInterval = null;
            }
        }
    }

    hideStreams() {
        // Clean up HLS instances
        if (this.hls) {
            this.hls.destroy();
            this.hls = null;
        }

        if (this.compressedHls) {
            this.compressedHls.destroy();
            this.compressedHls = null;
        }

        // Hide video containers
        this.hideVideoContainers();

        // Reset stream states
        this.compressedStreamLoaded = false;
        this.liveStreamShown = false;
        this.liveStream.src = '';
        this.compressedStream.src = '';

        // Clear monitoring intervals
        if (this.compressedStreamCheckInterval) {
            clearInterval(this.compressedStreamCheckInterval);
            this.compressedStreamCheckInterval = null;
        }

        // Reset overlays
        this.liveStreamOverlay.classList.remove('hidden');
        this.compressedStreamOverlay.classList.remove('hidden');

        this.liveStreamOverlay.innerHTML = `
            <i class="fas fa-spinner fa-spin"></i>
            <p>Connecting to live stream...</p>
        `;

        this.compressedStreamOverlay.innerHTML = `
            <i class="fas fa-clock"></i>
            <p>Waiting for compressed data...</p>
        `;

        // Hide file info displays
        this.hideFileInfo('original');
        this.hideFileInfo('compressed');
    }

    generateFinalReport() {
        const rawSizeText = this.rawSize.textContent;
        const compressedSizeText = this.compressedSize.textContent;
        const compressionRatioText = this.compressionRatio.textContent;
        const bandwidthSavingsText = this.bandwidthSavings.textContent;

        // Calculate space saved in bytes if we have the data
        let spaceSavedText = compressionRatioText;

        const reportHTML = `
            <div class="report-grid">
                <div class="report-item">
                    <h4>Original Size</h4>
                    <div class="value">${rawSizeText}</div>
                </div>
                <div class="report-item">
                    <h4>Compressed Size</h4>
                    <div class="value">${compressedSizeText}</div>
                </div>
                <div class="report-item">
                    <h4>Space Saved</h4>
                    <div class="value">${spaceSavedText}</div>
                </div>
                <div class="report-item">
                    <h4>Bandwidth Savings</h4>
                    <div class="value">${bandwidthSavingsText}</div>
                </div>
            </div>
            <div style="margin-top: 20px; padding: 15px; background: #2d2d2d; border-radius: 8px; border: 1px solid rgba(252, 149, 70, 0.2);">
                <h4 style="margin-bottom: 10px; color: #FC9546;">Summary</h4>
                <p style="color: #e2e8f0; line-height: 1.5;">Compression completed successfully. Files have been uploaded to S3 and local copies have been cleaned up.</p>
                <p style="margin-top: 10px; color: #FC9546;"><strong>Compression Results:</strong></p>
                <ul style="margin-left: 20px; margin-top: 5px; color: #639884; line-height: 1.6;">
                    <li>Original file size: <strong style="color: #e2e8f0;">${rawSizeText}</strong></li>
                    <li>Compressed file size: <strong style="color: #e2e8f0;">${compressedSizeText}</strong></li>
                    <li>Space saved: <strong style="color: #FC9546;">${spaceSavedText}</strong></li>
                    <li>Bandwidth reduction: <strong style="color: #FC9546;">${bandwidthSavingsText}</strong></li>
                </ul>
                <p style="margin-top: 15px; color: #FC9546;"><strong>S3 Buckets:</strong></p>
                <ul style="margin-left: 20px; margin-top: 5px; color: #639884;">
                    <li>Original: <code style="background: #1b1b1b; padding: 2px 6px; border-radius: 4px; color: #FC9546;">original-streams</code></li>
                    <li>Compressed: <code style="background: #1b1b1b; padding: 2px 6px; border-radius: 4px; color: #FC9546;">compressed-streams</code></li>
                </ul>
            </div>
        `;

        this.finalReport.innerHTML = reportHTML;
        this.reportSection.style.display = 'block';
        this.reportSection.classList.add('fade-in');
    }

    // S3 File Picker Methods
    initializeS3FilePicker() {
        // Initialize S3 file picker state
        this.currentS3Files = [];
        this.selectedS3File = null;
        this.currentS3StreamType = null;
    }

    async openS3FilePicker(streamType) {
        this.currentS3StreamType = streamType;
        this.selectedS3File = null;
        this.confirmS3Selection.disabled = true;

        // Show modal
        this.s3FileModal.style.display = 'flex';

        // Update modal title
        const modalTitle = this.s3FileModal.querySelector('.modal-header h2');
        modalTitle.innerHTML = `<i class="fas fa-cloud"></i> Select ${streamType === 'original' ? 'Original' : 'Compressed'} Video File`;

        // Show loading
        this.s3FileList.innerHTML = `
            <div class="loading">
                <i class="fas fa-spinner fa-spin"></i>
                <p>Loading S3 files...</p>
            </div>
        `;

        try {
            // Fetch S3 files
            const response = await fetch(`/api/s3/${streamType}`);
            if (!response.ok) {
                throw new Error('Failed to fetch S3 files');
            }

            const files = await response.json();
            this.currentS3Files = files;
            this.renderS3FileList(files);

        } catch (error) {
            console.error('Error loading S3 files:', error);
            this.s3FileList.innerHTML = `
                <div class="error-message">
                    <i class="fas fa-exclamation-triangle"></i>
                    <p>Error loading S3 files</p>
                    <small>${error.message}</small>
                </div>
            `;
        }
    }

    renderS3FileList(files) {
        if (files.length === 0) {
            this.s3FileList.innerHTML = `
                <div class="error-message">
                    <i class="fas fa-folder-open"></i>
                    <p>No MP4 files found</p>
                    <small>No video files available in the ${this.currentS3StreamType} S3 bucket</small>
                </div>
            `;
            return;
        }

        const fileListHTML = files.map((file, index) => {
            const fileName = file.key.split('/').pop();
            const fileSize = this.formatBytes(file.size);
            const lastModified = new Date(file.lastModified).toLocaleString();

            return `
                <div class="file-item" data-index="${index}">
                    <div class="file-item-icon">
                        <i class="fas fa-play"></i>
                    </div>
                    <div class="file-item-info">
                        <div class="file-item-name">${fileName}</div>
                        <div class="file-item-details">
                            <span>Size: ${fileSize}</span>
                            <span>Modified: ${lastModified}</span>
                        </div>
                    </div>
                </div>
            `;
        }).join('');

        this.s3FileList.innerHTML = fileListHTML;

        // Add click handlers to file items
        this.s3FileList.querySelectorAll('.file-item').forEach(item => {
            item.addEventListener('click', () => this.selectS3File(item));
        });
    }

    selectS3File(fileItem) {
        // Remove previous selection
        this.s3FileList.querySelectorAll('.file-item').forEach(item => {
            item.classList.remove('selected');
        });

        // Select current item
        fileItem.classList.add('selected');

        const fileIndex = parseInt(fileItem.dataset.index);
        this.selectedS3File = this.currentS3Files[fileIndex];
        this.confirmS3Selection.disabled = false;
    }

    async loadSelectedS3File() {
        if (!this.selectedS3File) return;

        try {
            // Generate pre-signed URL
            const response = await fetch('/api/s3/presign', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    bucket: this.currentS3StreamType === 'original' ? 'original-streams' : 'compressed-streams',
                    key: this.selectedS3File.key
                })
            });

            if (!response.ok) {
                throw new Error('Failed to generate pre-signed URL');
            }

            const result = await response.json();
            const fileName = this.selectedS3File.key.split('/').pop();
            const fileSize = this.formatBytes(this.selectedS3File.size);

            // Update file info display
            this.updateFileInfo(
                this.currentS3StreamType,
                fileName,
                fileSize,
                's3',
                this.selectedS3File.key
            );

            // Load the video based on stream type
            if (this.currentS3StreamType === 'original') {
                this.showLiveStreamContainer();
                this.loadOriginalMP4Stream(result.presignedUrl, fileName);
                this.liveStreamShown = true;
                this.addLog(`Loaded original S3 video: ${fileName}`, 'success');
            } else {
                this.showCompressedStreamContainer();
                this.loadCompressedMP4Stream(result.presignedUrl, fileName);
                this.addLog(`Loaded compressed S3 video: ${fileName}`, 'success');
            }

            this.closeS3FilePicker();

        } catch (error) {
            console.error('Error loading S3 file:', error);
            this.addLog(`Error loading S3 file: ${error.message}`, 'error');
        }
    }

    closeS3FilePicker() {
        this.s3FileModal.style.display = 'none';
        this.selectedS3File = null;
        this.currentS3StreamType = null;
        this.confirmS3Selection.disabled = true;
    }

    formatBytes(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // Update file info display
    updateFileInfo(streamType, fileName, fileSize, source = 'local', s3Key = null) {
        const isOriginal = streamType === 'original';
        const fileInfo = isOriginal ? this.originalFileInfo : this.compressedFileInfo;
        const fileNameElement = isOriginal ? this.originalFileName : this.compressedFileName;
        const fileSizeElement = isOriginal ? this.originalFileSize : this.compressedFileSize;
        const fileSourceElement = isOriginal ? this.originalFileSource : this.compressedFileSource;

        // Update content
        fileNameElement.textContent = fileName;
        fileSizeElement.textContent = fileSize;
        fileSourceElement.textContent = source === 's3' ? 'S3 Bucket' : 'Local File';

        // Update styling based on source
        fileInfo.className = `file-info ${source === 's3' ? 's3-file' : 'local-file'}`;

        // Show the file info
        fileInfo.style.display = 'block';
        fileInfo.classList.add('fade-in');

        // Add tooltip with full S3 key if available
        if (s3Key) {
            fileNameElement.title = s3Key;
        } else {
            fileNameElement.title = fileName;
        }

        this.addLog(`File info updated: ${fileName} (${fileSize}) - ${source}`, 'info');
    }

    // Hide file info display
    hideFileInfo(streamType) {
        const fileInfo = streamType === 'original' ? this.originalFileInfo : this.compressedFileInfo;
        fileInfo.style.display = 'none';
    }

    // Update file info for local files
    async updateLocalFileInfo(streamType, filename, mp4Url) {
        try {
            // Try to get file size from HEAD request
            const response = await fetch(mp4Url, { method: 'HEAD' });
            if (response.ok) {
                const contentLength = response.headers.get('content-length');
                const fileSize = contentLength ? this.formatBytes(parseInt(contentLength)) : 'Unknown size';

                this.updateFileInfo(streamType, filename, fileSize, 'local');
            } else {
                // Fallback to showing file info without size
                this.updateFileInfo(streamType, filename, 'Unknown size', 'local');
            }
        } catch (error) {
            console.log('Could not get file size for local file:', error);
            // Still show file info without size
            this.updateFileInfo(streamType, filename, 'Unknown size', 'local');
        }
    }
}

// Initialize the dashboard when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new RTSPDashboard();
});
