#!/usr/bin/env node
// ─────────────────────────────────────────────
// Script: test_compression_simulation.js
// Purpose:
//   - Simulate a compression process to test real-time updates
//   - Send periodic progress updates via WebSocket
// ─────────────────────────────────────────────

const WebSocket = require('ws');

const WS_URL = 'ws://localhost:4001';
const SESSION_ID = `test-${Date.now()}`;

console.log('🎬 Simulating compression process...');
console.log(`📡 Session ID: ${SESSION_ID}`);

const ws = new WebSocket(WS_URL);

let progressStep = 0;
const maxSteps = 10;

ws.on('open', () => {
    console.log('✅ WebSocket connected - starting simulation');
    
    // Send initial log
    ws.send(JSON.stringify({
        type: 'log',
        sessionId: SESSION_ID,
        data: {
            message: `🎬 Compression simulation started - Session: ${SESSION_ID}`,
            level: 'info',
            timestamp: new Date().toISOString()
        }
    }));
    
    // Send progress updates every 2 seconds
    const progressInterval = setInterval(() => {
        progressStep++;
        
        const originalSize = Math.floor(Math.random() * 1000000) + 5000000; // 5-6MB
        const encodedSize = Math.floor(originalSize * (0.3 + (progressStep / maxSteps) * 0.4)); // 30-70% compression
        const compressionRatio = ((originalSize - encodedSize) / originalSize * 100).toFixed(2);
        
        // Send log message
        ws.send(JSON.stringify({
            type: 'log',
            sessionId: SESSION_ID,
            data: {
                message: `📊 Progress update ${progressStep}/${maxSteps} - Compression: ${compressionRatio}%`,
                level: 'info',
                timestamp: new Date().toISOString()
            }
        }));
        
        // Send progress update
        ws.send(JSON.stringify({
            type: 'progress',
            sessionId: SESSION_ID,
            data: {
                originalSize,
                encodedSize,
                originalSizeFormatted: formatBytes(originalSize),
                encodedSizeFormatted: formatBytes(encodedSize),
                compressionRatio,
                timestamp: new Date().toISOString()
            }
        }));
        
        if (progressStep >= maxSteps) {
            clearInterval(progressInterval);
            
            // Send completion message
            setTimeout(() => {
                ws.send(JSON.stringify({
                    type: 'log',
                    sessionId: SESSION_ID,
                    data: {
                        message: '✅ Compression simulation completed successfully!',
                        level: 'success',
                        timestamp: new Date().toISOString()
                    }
                }));
                
                ws.send(JSON.stringify({
                    type: 'completed',
                    sessionId: SESSION_ID,
                    data: {
                        success: true,
                        exitCode: 0,
                        timestamp: new Date().toISOString()
                    }
                }));
                
                setTimeout(() => {
                    ws.close();
                }, 1000);
            }, 1000);
        }
    }, 2000);
});

ws.on('close', () => {
    console.log('🔌 Simulation completed');
    process.exit(0);
});

ws.on('error', (error) => {
    console.error('❌ WebSocket error:', error.message);
    process.exit(1);
});

// Helper function to format bytes
function formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Timeout to prevent hanging
setTimeout(() => {
    console.log('⏰ Simulation timeout');
    process.exit(1);
}, 30000);
