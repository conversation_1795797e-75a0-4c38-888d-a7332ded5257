# 🎬 HLS Streaming Integration Guide

This guide explains how to use the new HLS (HTTP Live Streaming) functionality that converts .ts files to web-streamable HLS format and uploads them to S3.

## 🚀 Overview

The system now supports converting captured RTSP streams to HLS format for optimal web streaming:

1. **RTSP Capture** → `.ts` file (existing functionality)
2. **HLS Conversion** → `.m3u8` playlist + `.ts` segments
3. **S3 Upload** → Web-accessible streaming URLs
4. **Web Dashboard** → Automatic HLS playback

## 📋 New Scripts

### 1. `convert_to_hls_and_upload.sh`
Converts a single .ts file to HLS format and uploads to S3.

```bash
./convert_to_hls_and_upload.sh <TS_FILE> <S3_BUCKET> [S3_PREFIX] [SESSION_ID]
```

**Example:**
```bash
./convert_to_hls_and_upload.sh original/stream/stream_20241217_143022.ts my-streaming-bucket streams/ session123
```

### 2. `auto_process_to_hls.sh`
Automatically monitors for new .ts files and processes them.

```bash
# Monitor mode (continuous)
S3_BUCKET=my-bucket ./auto_process_to_hls.sh monitor

# Manual mode (process existing files)
S3_BUCKET=my-bucket ./auto_process_to_hls.sh manual
```

### 3. `post_process_stream.sh`
Simple wrapper for post-processing individual streams.

```bash
./post_process_stream.sh original/stream/stream_20241217_143022.ts session123
```

## 🔧 Configuration

Set these environment variables:

```bash
export S3_BUCKET="your-streaming-bucket"
export S3_PREFIX="streams/"  # Optional
```

## 🌐 Web Dashboard Integration

The web dashboard now automatically:

1. **Checks for HLS streams first** (preferred for web streaming)
2. **Falls back to local files** if HLS not available
3. **Uses HLS.js** for cross-browser compatibility
4. **Displays stream source** (Local/S3/HLS)

### How it works:

1. When a stream is captured, the session ID is extracted from the filename
2. HLS conversion creates: `hls/{session_id}/{stream_name}/stream_url.txt`
3. Web dashboard polls `/api/hls-streams/{session_id}` for available HLS streams
4. When found, loads the S3 HLS URL using HLS.js

## 📁 Directory Structure

```
zmt-live/
├── original/stream/              # Raw .ts files
├── hls/                         # HLS conversion output
│   └── {session_id}/
│       └── {stream_name}/
│           ├── playlist.m3u8    # HLS playlist
│           ├── segment_*.ts     # HLS segments
│           └── stream_url.txt   # S3 URL for web dashboard
├── processed/                   # Processing markers
└── logs/                       # Conversion logs
```

## 🎯 Usage Workflows

### Workflow 1: Manual Processing
```bash
# 1. Capture RTSP stream (existing)
./save_rtsp_raw_stream.sh rtsp://camera.ip/stream1 00:05:00

# 2. Convert to HLS and upload
S3_BUCKET=my-bucket ./post_process_stream.sh original/stream/stream_20241217_143022.ts

# 3. Stream is now available in web dashboard
```

### Workflow 2: Automatic Processing
```bash
# 1. Start auto-processor in background
S3_BUCKET=my-bucket ./auto_process_to_hls.sh monitor &

# 2. Capture streams as usual
./save_rtsp_raw_stream.sh rtsp://camera.ip/stream1 00:05:00

# 3. Files are automatically processed and available in web dashboard
```

### Workflow 3: Batch Processing
```bash
# Process all existing .ts files
S3_BUCKET=my-bucket ./auto_process_to_hls.sh manual
```

## 🔍 Monitoring

### Check Processing Status
```bash
# View processing logs
tail -f logs/hls_conversion_*.log

# Check processed files
ls processed/

# View HLS output
ls -la hls/
```

### Web Dashboard
- Open `http://localhost:3000`
- Start a compression session
- The compressed stream will automatically load from S3 HLS when available
- Look for "HLS Stream Ready" indicator

## ⚙️ S3 Configuration

### Bucket Setup
```bash
# Create bucket (if needed)
aws s3 mb s3://your-streaming-bucket

# Set public read policy for web access
aws s3api put-bucket-policy --bucket your-streaming-bucket --policy '{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Sid": "PublicReadGetObject",
      "Effect": "Allow",
      "Principal": "*",
      "Action": "s3:GetObject",
      "Resource": "arn:aws:s3:::your-streaming-bucket/*"
    }
  ]
}'
```

### CORS Configuration (for web access)
```json
[
  {
    "AllowedHeaders": ["*"],
    "AllowedMethods": ["GET", "HEAD"],
    "AllowedOrigins": ["*"],
    "ExposeHeaders": []
  }
]
```

## 🎬 HLS Stream URLs

After processing, streams are available at:
```
https://your-streaming-bucket.s3.amazonaws.com/streams/{stream_name}/playlist.m3u8
```

These URLs can be used in:
- **Web browsers** (with HLS.js)
- **Video players** (VLC, etc.)
- **Mobile apps**
- **Streaming platforms**

## 🔧 Troubleshooting

### Common Issues

1. **AWS CLI not configured**
   ```bash
   aws configure
   ```

2. **S3 permissions**
   - Ensure bucket has public read access
   - Check CORS configuration

3. **FFmpeg not found**
   ```bash
   # Ubuntu/Debian
   sudo apt-get install ffmpeg
   
   # macOS
   brew install ffmpeg
   ```

4. **inotify-tools missing** (for monitoring)
   ```bash
   # Ubuntu/Debian
   sudo apt-get install inotify-tools
   ```

### Debug Mode
```bash
# Enable verbose logging
export DEBUG=1
./convert_to_hls_and_upload.sh ...
```

## 🎉 Benefits

- **Web-optimized streaming** with HLS format
- **Cross-browser compatibility** via HLS.js
- **Scalable delivery** through S3 CDN
- **Automatic integration** with existing dashboard
- **Mobile-friendly** streaming
- **Bandwidth efficient** with adaptive segments
