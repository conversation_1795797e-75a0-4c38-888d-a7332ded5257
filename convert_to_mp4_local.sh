#!/bin/bash
# ─────────────────────────────────────────────
# Script: convert_to_mp4_local.sh
# Purpose:
#   - Convert .ts file to .mp4 format
#   - Keep MP4 files locally for web serving
#   - Upload both .ts and .mp4 files to S3
#   - Use simple names without timestamps to save space
# ─────────────────────────────────────────────

# 📥 Parse input arguments
TS_FILE="$1"
SESSION_ID="$2" # Optional session ID for web dashboard integration

if [ -z "$TS_FILE" ]; then
    echo "❗ Usage: $0 <TS_FILE> [SESSION_ID]"
    echo "   Example: $0 original/stream/stream_123.ts session123"
    echo "   Example: $0 encoded/stream/stream_123.ts session123"
    exit 1
fi

# ✅ Check if input file exists
if [ ! -f "$TS_FILE" ]; then
    echo "❌ Input file not found: $TS_FILE"
    exit 1
fi

# 📂 Create output directories
MP4_DIR="mp4"
mkdir -p "$MP4_DIR"
mkdir -p logs

# 🕒 Timestamp for logging
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
DATE_PATH=$(date +"%Y/%m/%d")
LOG_FILE="logs/mp4_conversion_${TIMESTAMP}.log"

# 🪣 S3 Configuration
if [[ "$TS_FILE" == *"original"* ]]; then
    S3_BUCKET="original-streams"
else
    S3_BUCKET="compressed-streams"
fi

# ☁️ Get AWS region
AWS_REGION=$(aws configure get region)
if [ -z "$AWS_REGION" ]; then
    echo "❌ AWS CLI region not configured. Run: aws configure" | tee -a "$LOG_FILE"
    exit 1
fi

echo "🎬 Converting .ts to .mp4 format and uploading to S3..." | tee -a "$LOG_FILE"
echo "📁 Input: $TS_FILE" | tee -a "$LOG_FILE"
echo "☁️ S3 Bucket: $S3_BUCKET" | tee -a "$LOG_FILE"

# 📊 Get and report file size before processing
if [ -f "$TS_FILE" ]; then
    FILE_SIZE=$(stat -f%z "$TS_FILE" 2>/dev/null || stat -c%s "$TS_FILE" 2>/dev/null)
    echo "📊 Input file size: $FILE_SIZE bytes" | tee -a "$LOG_FILE"
fi

# ☁️ Create bucket if it doesn't exist
ensure_bucket() {
    BUCKET_NAME=$1
    if ! aws s3api head-bucket --bucket "$BUCKET_NAME" 2>/dev/null; then
        echo "🪣 Creating bucket: $BUCKET_NAME" | tee -a "$LOG_FILE"
        if [ "$AWS_REGION" == "us-east-1" ]; then
            aws s3api create-bucket --bucket "$BUCKET_NAME" 2>> "$LOG_FILE"
        else
            aws s3api create-bucket --bucket "$BUCKET_NAME" \
                --region "$AWS_REGION" \
                --create-bucket-configuration LocationConstraint="$AWS_REGION" 2>> "$LOG_FILE"
        fi
    else
        echo "✅ Bucket exists: $BUCKET_NAME" | tee -a "$LOG_FILE"
    fi
}

# 🪣 Ensure S3 bucket exists
ensure_bucket "$S3_BUCKET"

# 🔄 Determine output file name based on input path
if [[ "$TS_FILE" == *"original"* ]]; then
    OUTPUT_MP4="${MP4_DIR}/original.mp4"
    S3_FILENAME="original.mp4"
    echo "📁 Output: $OUTPUT_MP4 (original stream)" | tee -a "$LOG_FILE"
else
    OUTPUT_MP4="${MP4_DIR}/compressed.mp4"
    S3_FILENAME="compressed.mp4"
    echo "📁 Output: $OUTPUT_MP4 (compressed stream)" | tee -a "$LOG_FILE"
fi

# 🔍 Check if input file is valid before conversion
FILE_SIZE=$(stat -c %s "$TS_FILE" 2>/dev/null || echo "0")
echo "📊 Input file size: $FILE_SIZE bytes" | tee -a "$LOG_FILE"

if [ "$FILE_SIZE" -eq 0 ]; then
    echo "❌ Input file is empty (0 bytes). Cannot convert to MP4." | tee -a "$LOG_FILE"
    echo "💡 This usually means the RTSP capture failed or was interrupted." | tee -a "$LOG_FILE"
    exit 1
fi

if [ "$FILE_SIZE" -lt 1024 ]; then
    echo "⚠️  Input file is very small ($FILE_SIZE bytes). This may indicate a corrupted capture." | tee -a "$LOG_FILE"
    echo "🔍 Attempting conversion anyway..." | tee -a "$LOG_FILE"
fi

# 🔄 Convert to MP4 using ffmpeg (simple copy, no re-encoding)
echo "🎬 Starting MP4 conversion..." | tee -a "$LOG_FILE"
ffmpeg -y -hide_banner -loglevel info \
    -i "$TS_FILE" \
    -c copy \
    "$OUTPUT_MP4" 2>> "$LOG_FILE"

# ✅ Check conversion success
FFMPEG_EXIT_CODE=$?
if [ $FFMPEG_EXIT_CODE -ne 0 ]; then
    echo "❌ MP4 conversion failed with exit code: $FFMPEG_EXIT_CODE" | tee -a "$LOG_FILE"
    echo "📋 Common causes:" | tee -a "$LOG_FILE"
    echo "   - Empty or corrupted input file" | tee -a "$LOG_FILE"
    echo "   - Invalid RTSP stream format" | tee -a "$LOG_FILE"
    echo "   - Network interruption during capture" | tee -a "$LOG_FILE"
    echo "💡 Try running: ./cleanup_empty_streams.sh to remove problematic files" | tee -a "$LOG_FILE"
    exit 1
fi

# 🔍 Verify output file was created and has content
if [ ! -f "$OUTPUT_MP4" ]; then
    echo "❌ Output MP4 file was not created: $OUTPUT_MP4" | tee -a "$LOG_FILE"
    exit 1
fi

OUTPUT_SIZE=$(stat -c %s "$OUTPUT_MP4" 2>/dev/null || echo "0")
if [ "$OUTPUT_SIZE" -eq 0 ]; then
    echo "❌ Output MP4 file is empty: $OUTPUT_MP4" | tee -a "$LOG_FILE"
    rm -f "$OUTPUT_MP4"
    exit 1
fi

echo "✅ MP4 conversion completed successfully" | tee -a "$LOG_FILE"
echo "📊 Output file size: $(du -h "$OUTPUT_MP4" | cut -f1) ($(stat -c %s "$OUTPUT_MP4") bytes)" | tee -a "$LOG_FILE"

# ☁️ Upload original .ts file to S3
echo "⬆️ Uploading original .ts file to S3..." | tee -a "$LOG_FILE"
TS_BASENAME=$(basename "$TS_FILE")
TS_S3_PATH="s3://$S3_BUCKET/$DATE_PATH/$TS_BASENAME"
echo "📤 S3 destination: $TS_S3_PATH" | tee -a "$LOG_FILE"

if aws s3 cp "$TS_FILE" "$TS_S3_PATH" 2>> "$LOG_FILE"; then
    echo "✅ .ts file uploaded successfully" | tee -a "$LOG_FILE"
else
    echo "❌ .ts file upload failed" | tee -a "$LOG_FILE"
fi

# ☁️ Upload MP4 file to S3
echo "⬆️ Uploading MP4 file to S3..." | tee -a "$LOG_FILE"
MP4_S3_PATH="s3://$S3_BUCKET/$DATE_PATH/$S3_FILENAME"
echo "📤 S3 destination: $MP4_S3_PATH" | tee -a "$LOG_FILE"

if aws s3 cp "$OUTPUT_MP4" "$MP4_S3_PATH" 2>> "$LOG_FILE"; then
    echo "✅ MP4 file uploaded successfully" | tee -a "$LOG_FILE"

    # Generate S3 pre-signed URL for web access (24-hour expiration)
    S3_PRESIGNED_URL=$(aws s3 presign "$MP4_S3_PATH" --expires-in 86400 2>/dev/null)
    if [ -n "$S3_PRESIGNED_URL" ]; then
        echo "🔗 S3 Pre-signed URL (24h): $S3_PRESIGNED_URL" | tee -a "$LOG_FILE"
        echo "$S3_PRESIGNED_URL" > "${MP4_DIR}/${S3_FILENAME%.mp4}_s3_url.txt"
    fi
else
    echo "❌ MP4 file upload failed" | tee -a "$LOG_FILE"
fi

# 📁 Keep MP4 locally for web serving
echo "💾 Keeping MP4 file locally for web serving..." | tee -a "$LOG_FILE"

# Notify web dashboard that processing is starting (show live stream container)
if [ -n "$SESSION_ID" ]; then
    node notify_websocket.js upload_started "$SESSION_ID" 2>/dev/null || echo "WebSocket notification failed (server may not be running)"
fi

# Create local URL for web serving
LOCAL_URL="/mp4/${S3_FILENAME}"

echo "✅ MP4 file ready for local serving and S3 access" | tee -a "$LOG_FILE"
echo "" | tee -a "$LOG_FILE"
echo "🎯 Local MP4 Video URL:" | tee -a "$LOG_FILE"
echo "$LOCAL_URL" | tee -a "$LOG_FILE"
echo "" | tee -a "$LOG_FILE"
echo "☁️ S3 MP4 Path:" | tee -a "$LOG_FILE"
echo "$MP4_S3_PATH" | tee -a "$LOG_FILE"
if [ -n "$S3_PRESIGNED_URL" ]; then
    echo "" | tee -a "$LOG_FILE"
    echo "🔗 S3 Pre-signed URL (24h):" | tee -a "$LOG_FILE"
    echo "$S3_PRESIGNED_URL" | tee -a "$LOG_FILE"
fi
echo "" | tee -a "$LOG_FILE"
echo "📋 Use local URL for immediate access, S3 URL for external sharing" | tee -a "$LOG_FILE"

# Save URLs to files for easy access
echo "$LOCAL_URL" > "${MP4_DIR}/${S3_FILENAME%.mp4}_url.txt"
if [ -n "$S3_PRESIGNED_URL" ]; then
    echo "$S3_PRESIGNED_URL" > "${MP4_DIR}/${S3_FILENAME%.mp4}_s3_url.txt"
fi

# If SESSION_ID is provided, also save for web dashboard integration
if [ -n "$SESSION_ID" ]; then
    WEB_MP4_DIR="mp4/${SESSION_ID}"
    mkdir -p "$WEB_MP4_DIR"
    echo "$LOCAL_URL" > "${WEB_MP4_DIR}/${S3_FILENAME%.mp4}_url.txt"
    echo "📱 Web dashboard integration: ${WEB_MP4_DIR}/${S3_FILENAME%.mp4}_url.txt" | tee -a "$LOG_FILE"

    # Notify web dashboard that MP4 is ready
    node notify_websocket.js mp4_ready "$SESSION_ID" "{\"url\":\"$LOCAL_URL\",\"filename\":\"$S3_FILENAME\",\"type\":\"${S3_FILENAME%.mp4}\"}" 2>/dev/null || echo "WebSocket notification failed"
fi

echo "🎉 Process completed! MP4 video ready for local web serving and S3 access." | tee -a "$LOG_FILE"
