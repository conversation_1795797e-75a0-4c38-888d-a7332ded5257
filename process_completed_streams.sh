#!/bin/bash
# ─────────────────────────────────────────────
# Script: process_completed_streams.sh
# Purpose:
#   - Monitor for completed .ts files
#   - Automatically convert to HLS and upload to S3
#   - Can be run manually or as a background process
# ─────────────────────────────────────────────

# 📋 Configuration
S3_BUCKET="${S3_BUCKET:-your-streaming-bucket}"  # Set via environment or modify here
S3_PREFIX="${S3_PREFIX:-streams/}"               # Optional S3 prefix
ORIGINAL_DIR="original/stream"
PROCESSED_DIR="processed"

# Create directories
mkdir -p "$PROCESSED_DIR"
mkdir -p logs

# 📥 Parse arguments
MODE="${1:-manual}"  # manual or monitor

if [ "$MODE" = "help" ] || [ "$MODE" = "--help" ]; then
    echo "📖 Usage: $0 [MODE]"
    echo ""
    echo "Modes:"
    echo "  manual   - Process all existing .ts files once (default)"
    echo "  monitor  - Continuously monitor for new .ts files"
    echo "  help     - Show this help"
    echo ""
    echo "Environment variables:"
    echo "  S3_BUCKET - S3 bucket name (default: your-streaming-bucket)"
    echo "  S3_PREFIX - S3 path prefix (default: streams/)"
    echo ""
    echo "Examples:"
    echo "  S3_BUCKET=my-bucket ./process_completed_streams.sh manual"
    echo "  S3_BUCKET=my-bucket S3_PREFIX=live/ ./process_completed_streams.sh monitor"
    exit 0
fi

# 🔧 Function to process a single .ts file
process_ts_file() {
    local ts_file="$1"
    local basename=$(basename "$ts_file")
    local processed_marker="${PROCESSED_DIR}/${basename}.processed"
    
    # Skip if already processed
    if [ -f "$processed_marker" ]; then
        echo "⏭️ Already processed: $basename"
        return 0
    fi
    
    echo "🔄 Processing: $ts_file"
    
    # Convert to HLS and upload
    if ./convert_to_hls_and_upload.sh "$ts_file" "$S3_BUCKET" "$S3_PREFIX"; then
        # Mark as processed
        touch "$processed_marker"
        echo "✅ Completed: $basename"
        
        # Log the stream URL
        local hls_basename=$(basename "$ts_file" .ts)
        local url_file="hls/${hls_basename}/stream_url.txt"
        if [ -f "$url_file" ]; then
            echo "🌐 Stream URL: $(cat "$url_file")"
        fi
    else
        echo "❌ Failed to process: $basename"
        return 1
    fi
}

# 🎯 Manual mode - process all existing files
if [ "$MODE" = "manual" ]; then
    echo "🔍 Processing all .ts files in $ORIGINAL_DIR..."
    
    if [ ! -d "$ORIGINAL_DIR" ]; then
        echo "❌ Directory not found: $ORIGINAL_DIR"
        exit 1
    fi
    
    # Find and process all .ts files
    find "$ORIGINAL_DIR" -name "*.ts" -type f | while read -r ts_file; do
        process_ts_file "$ts_file"
    done
    
    echo "🎉 Manual processing completed!"

# 👀 Monitor mode - watch for new files
elif [ "$MODE" = "monitor" ]; then
    echo "👀 Monitoring $ORIGINAL_DIR for new .ts files..."
    echo "Press Ctrl+C to stop monitoring"
    
    if ! command -v inotifywait &> /dev/null; then
        echo "❌ inotifywait not found. Install inotify-tools:"
        echo "   Ubuntu/Debian: sudo apt-get install inotify-tools"
        echo "   CentOS/RHEL: sudo yum install inotify-tools"
        exit 1
    fi
    
    # Monitor for file creation and modification
    inotifywait -m -r -e close_write,moved_to "$ORIGINAL_DIR" --format '%w%f' | while read -r file; do
        if [[ "$file" == *.ts ]]; then
            echo "📁 New file detected: $file"
            # Small delay to ensure file is completely written
            sleep 2
            process_ts_file "$file"
        fi
    done

else
    echo "❌ Unknown mode: $MODE"
    echo "Use 'manual', 'monitor', or 'help'"
    exit 1
fi
