#!/bin/bash
# ─────────────────────────────────────────────
# Script: cleanup_empty_streams.sh
# Purpose:
#   - Find and remove empty .ts files (0 bytes)
#   - Clean up corrupted stream files
#   - Prevent MP4 conversion errors from empty files
# ─────────────────────────────────────────────

echo "🧹 Cleaning up empty and corrupted stream files..."

# Function to check and remove empty files
cleanup_empty_files() {
    local dir="$1"
    local description="$2"
    
    if [ ! -d "$dir" ]; then
        echo "📁 Directory not found: $dir"
        return
    fi
    
    echo "🔍 Checking $description in $dir..."
    
    # Find empty .ts files (0 bytes)
    empty_files=$(find "$dir" -name "*.ts" -type f -size 0 2>/dev/null)
    
    if [ -n "$empty_files" ]; then
        echo "❌ Found empty files:"
        echo "$empty_files" | while read -r file; do
            echo "   - $file ($(stat -c %s "$file" 2>/dev/null || echo "0") bytes)"
            rm -f "$file"
            echo "   ✅ Removed: $file"
        done
    else
        echo "✅ No empty files found in $dir"
    fi
    
    # Find very small files (likely corrupted, less than 1KB)
    small_files=$(find "$dir" -name "*.ts" -type f -size -1024c 2>/dev/null)
    
    if [ -n "$small_files" ]; then
        echo "⚠️  Found suspiciously small files (< 1KB):"
        echo "$small_files" | while read -r file; do
            size=$(stat -c %s "$file" 2>/dev/null || echo "0")
            echo "   - $file ($size bytes)"
            echo "   🤔 Consider removing if this seems corrupted"
        done
    fi
}

# Clean up directories
cleanup_empty_files "original/stream" "original streams"
cleanup_empty_files "encoded/stream" "compressed streams"

# Check for any remaining .ts files
echo ""
echo "📊 Summary of remaining .ts files:"
for dir in "original/stream" "encoded/stream"; do
    if [ -d "$dir" ]; then
        count=$(find "$dir" -name "*.ts" -type f 2>/dev/null | wc -l)
        if [ "$count" -gt 0 ]; then
            echo "📁 $dir: $count files"
            find "$dir" -name "*.ts" -type f -exec ls -lh {} \; 2>/dev/null | head -5
            if [ "$count" -gt 5 ]; then
                echo "   ... and $((count - 5)) more files"
            fi
        else
            echo "📁 $dir: No .ts files"
        fi
    fi
done

echo ""
echo "✅ Cleanup completed!"
echo ""
echo "💡 Tips to prevent empty files:"
echo "   - Ensure RTSP URL is accessible before starting capture"
echo "   - Check network connectivity to RTSP source"
echo "   - Verify RTSP credentials if authentication is required"
echo "   - Test with shorter durations first (1-2 minutes)"
