🎬 Converting .ts to .mp4 format and uploading to S3...
📁 Input: /home/<USER>/Documents/augment-projects/zmt-live/encoded/stream/stream_20250702_153455_zmt.ts
☁️ S3 Bucket: compressed-streams
📊 Input file size: 1713620 bytes
✅ Bucket exists: compressed-streams
📁 Output: mp4/compressed.mp4 (compressed stream)
Input #0, mpegts, from '/home/<USER>/Documents/augment-projects/zmt-live/encoded/stream/stream_20250702_153455_zmt.ts':
  Duration: 00:00:59.20, start: 2.200000, bitrate: 231 kb/s
  Program 1 
    Metadata:
      service_name    : Stream
      service_provider: FFmpeg
  Stream #0:0[0x100]: Video: h264 (High) ([27][0][0][0] / 0x001B), yuvj420p(pc, bt709, progressive), 1024x768 [SAR 128:129 DAR 512:387], 30 fps, 30 tbr, 90k tbn
Output #0, mp4, to 'mp4/compressed.mp4':
  Metadata:
    encoder         : Lavf60.16.100
  Stream #0:0: Video: h264 (High) (avc1 / 0x31637661), yuvj420p(pc, bt709, progressive), 1024x768 [SAR 128:129 DAR 512:387], q=2-31, 30 fps, 30 tbr, 90k tbn
Stream mapping:
  Stream #0:0 -> #0:0 (copy)
Press [q] to stop, [?] for help
size=       0kB time=-00:00:00.06 bitrate=  -0.0kbits/s speed=N/A    
[out#0/mp4 @ 0x5b9e4903a480] video:1209kB audio:0kB subtitle:0kB other streams:0kB global headers:0kB muxing overhead: 0.931223%
size=    1220kB time=00:00:59.10 bitrate= 169.1kbits/s speed=7.22e+03x    
✅ MP4 conversion completed
⬆️ Uploading original .ts file to S3...
📤 S3 destination: s3://compressed-streams/2025/07/02/stream_20250702_153455_zmt.ts
✅ .ts file uploaded successfully
⬆️ Uploading MP4 file to S3...
📤 S3 destination: s3://compressed-streams/2025/07/02/compressed.mp4
✅ MP4 file uploaded successfully
🔗 S3 Pre-signed URL (24h): https://compressed-streams.s3.us-west-1.amazonaws.com/2025/07/02/compressed.mp4?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIARWTHWVS7NV2YMDHW%2F20250702%2Fus-west-1%2Fs3%2Faws4_request&X-Amz-Date=20250702T193610Z&X-Amz-Expires=86400&X-Amz-SignedHeaders=host&X-Amz-Signature=b90956a8b9d5838f0b9e0a33fa5a253d6b3fec53a785f1e4c16978d355a3ae9e
💾 Keeping MP4 file locally for web serving...
✅ MP4 file ready for local serving and S3 access

🎯 Local MP4 Video URL:
/mp4/compressed.mp4

☁️ S3 MP4 Path:
s3://compressed-streams/2025/07/02/compressed.mp4

🔗 S3 Pre-signed URL (24h):
https://compressed-streams.s3.us-west-1.amazonaws.com/2025/07/02/compressed.mp4?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIARWTHWVS7NV2YMDHW%2F20250702%2Fus-west-1%2Fs3%2Faws4_request&X-Amz-Date=20250702T193610Z&X-Amz-Expires=86400&X-Amz-SignedHeaders=host&X-Amz-Signature=b90956a8b9d5838f0b9e0a33fa5a253d6b3fec53a785f1e4c16978d355a3ae9e

📋 Use local URL for immediate access, S3 URL for external sharing
📱 Web dashboard integration: mp4/20250702_153455/compressed_url.txt
🎉 Process completed! MP4 video ready for local web serving and S3 access.
