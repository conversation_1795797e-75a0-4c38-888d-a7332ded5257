🎬 Converting .ts to .mp4 format and uploading to S3...
📁 Input: /home/<USER>/Documents/augment-projects/zmt-live/encoded/stream/stream_20250729_145159_zmt.ts
☁️ S3 Bucket: compressed-streams
📊 Input file size: 1706476 bytes
✅ Bucket exists: compressed-streams
📁 Output: mp4/compressed.mp4 (compressed stream)
📊 Input file size: 1706476 bytes
🎬 Starting MP4 conversion...
Input #0, mpegts, from '/home/<USER>/Documents/augment-projects/zmt-live/encoded/stream/stream_20250729_145159_zmt.ts':
  Duration: 00:00:59.97, start: 1.466667, bitrate: 227 kb/s
  Program 1 
    Metadata:
      service_name    : Stream
      service_provider: FFmpeg
  Stream #0:0[0x100]: Video: h264 (High) ([27][0][0][0] / 0x001B), yuvj420p(pc, bt709, progressive), 1024x768 [SAR 128:129 DAR 512:387], 30 fps, 30 tbr, 90k tbn
Output #0, mp4, to 'mp4/compressed.mp4':
  Metadata:
    encoder         : Lavf60.16.100
  Stream #0:0: Video: h264 (High) (avc1 / 0x31637661), yuvj420p(pc, bt709, progressive), 1024x768 [SAR 128:129 DAR 512:387], q=2-31, 30 fps, 30 tbr, 90k tbn
Stream mapping:
  Stream #0:0 -> #0:0 (copy)
Press [q] to stop, [?] for help
size=       0kB time=-00:00:00.06 bitrate=  -0.0kbits/s speed=N/A    
[out#0/mp4 @ 0x568c9c96db80] video:1200kB audio:0kB subtitle:0kB other streams:0kB global headers:0kB muxing overhead: 1.225811%
size=    1215kB time=00:00:59.86 bitrate= 166.2kbits/s speed=7.75e+03x    
✅ MP4 conversion completed successfully
📊 Output file size: 1.2M (1243965 bytes)
⬆️ Uploading original .ts file to S3...
📤 S3 destination: s3://compressed-streams/2025/07/29/stream_20250729_145159_zmt.ts
✅ .ts file uploaded successfully
⬆️ Uploading MP4 file to S3...
📤 S3 destination: s3://compressed-streams/2025/07/29/compressed.mp4
