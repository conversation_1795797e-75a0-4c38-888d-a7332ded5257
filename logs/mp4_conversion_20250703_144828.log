🎬 Converting .ts to .mp4 format and uploading to S3...
📁 Input: /home/<USER>/Documents/augment-projects/zmt-live/encoded/stream/stream_20250626_121844_zmt.ts
☁️ S3 Bucket: compressed-streams
📊 Input file size: 0 bytes
✅ Bucket exists: compressed-streams
📁 Output: mp4/compressed.mp4 (compressed stream)
[in#0 @ 0x57f0e8595740] Error opening input: Invalid data found when processing input
Error opening input file /home/<USER>/Documents/augment-projects/zmt-live/encoded/stream/stream_20250626_121844_zmt.ts.
Error opening input files: Invalid data found when processing input
❌ MP4 conversion failed. Check log: logs/mp4_conversion_20250703_144828.log
