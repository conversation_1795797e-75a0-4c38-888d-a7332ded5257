🎬 Converting .ts to .mp4 format and uploading to S3...
📁 Input: test_files/encoded/stream/small_test.ts
☁️ S3 Bucket: compressed-streams
📊 Input file size: 10 bytes
✅ Bucket exists: compressed-streams
📁 Output: mp4/compressed.mp4 (compressed stream)
📊 Input file size: 10 bytes
⚠️  Input file is very small (10 bytes). This may indicate a corrupted capture.
🔍 Attempting conversion anyway...
🎬 Starting MP4 conversion...
[in#0 @ 0x638d78678740] Error opening input: Invalid data found when processing input
Error opening input file test_files/encoded/stream/small_test.ts.
Error opening input files: Invalid data found when processing input
❌ MP4 conversion failed with exit code: 183
📋 Common causes:
   - Empty or corrupted input file
   - Invalid RTSP stream format
   - Network interruption during capture
💡 Try running: ./cleanup_empty_streams.sh to remove problematic files
