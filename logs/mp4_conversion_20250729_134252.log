🎬 Converting .ts to .mp4 format and uploading to S3...
📁 Input: /home/<USER>/Documents/augment-projects/zmt-live/original/stream/stream_20250729_134149.ts
☁️ S3 Bucket: original-streams
📊 Input file size: 7652352 bytes
✅ Bucket exists: original-streams
📁 Output: mp4/original.mp4 (original stream)
Input #0, mpegts, from '/home/<USER>/Documents/augment-projects/zmt-live/original/stream/stream_20250729_134149.ts':
  Duration: 00:00:59.13, start: 2.300000, bitrate: 1035 kb/s
  Program 1 
    Metadata:
      service_name    : Stream
      service_provider: FFmpeg
  Stream #0:0[0x100]: Video: h264 (High) ([27][0][0][0] / 0x001B), yuvj420p(pc, bt709, progressive), 1024x768 [SAR 128:129 DAR 512:387], 30 fps, 30 tbr, 90k tbn
Output #0, mp4, to 'mp4/original.mp4':
  Metadata:
    encoder         : Lavf60.16.100
  Stream #0:0: Video: h264 (High) (avc1 / 0x31637661), yuvj420p(pc, bt709, progressive), 1024x768 [SAR 128:129 DAR 512:387], q=2-31, 30 fps, 30 tbr, 90k tbn
Stream mapping:
  Stream #0:0 -> #0:0 (copy)
Press [q] to stop, [?] for help
size=       0kB time=00:00:00.00 bitrate=N/A speed=N/A    
[out#0/mp4 @ 0x5bd174967e40] video:6889kB audio:0kB subtitle:0kB other streams:0kB global headers:0kB muxing overhead: 0.253392%
size=    6906kB time=00:00:59.09 bitrate= 957.4kbits/s speed=3.94e+03x    
✅ MP4 conversion completed
⬆️ Uploading original .ts file to S3...
📤 S3 destination: s3://original-streams/2025/07/29/stream_20250729_134149.ts
✅ .ts file uploaded successfully
⬆️ Uploading MP4 file to S3...
📤 S3 destination: s3://original-streams/2025/07/29/original.mp4
✅ MP4 file uploaded successfully
🔗 S3 Pre-signed URL (24h): https://original-streams.s3.us-east-1.amazonaws.com/2025/07/29/original.mp4?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIARWTHWVS7NV2YMDHW%2F20250729%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250729T174301Z&X-Amz-Expires=86400&X-Amz-SignedHeaders=host&X-Amz-Signature=9beb94c5543bdabe9661d3ed9b7285995d4507a4fe44378087a07c29d05b8f66
💾 Keeping MP4 file locally for web serving...
✅ MP4 file ready for local serving and S3 access

🎯 Local MP4 Video URL:
/mp4/original.mp4

☁️ S3 MP4 Path:
s3://original-streams/2025/07/29/original.mp4

🔗 S3 Pre-signed URL (24h):
https://original-streams.s3.us-east-1.amazonaws.com/2025/07/29/original.mp4?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIARWTHWVS7NV2YMDHW%2F20250729%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250729T174301Z&X-Amz-Expires=86400&X-Amz-SignedHeaders=host&X-Amz-Signature=9beb94c5543bdabe9661d3ed9b7285995d4507a4fe44378087a07c29d05b8f66

📋 Use local URL for immediate access, S3 URL for external sharing
📱 Web dashboard integration: mp4/20250729_134149/original_url.txt
🎉 Process completed! MP4 video ready for local web serving and S3 access.
