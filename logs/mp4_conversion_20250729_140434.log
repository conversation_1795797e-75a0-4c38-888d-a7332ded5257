🎬 Converting .ts to .mp4 format and uploading to S3...
📁 Input: test_small.ts
☁️ S3 Bucket: compressed-streams
📊 Input file size: 13 bytes
✅ Bucket exists: compressed-streams
📁 Output: mp4/compressed.mp4 (compressed stream)
📊 Input file size: 13 bytes
⚠️  Input file is very small (13 bytes). This may indicate a corrupted capture.
🔍 Attempting conversion anyway...
🎬 Starting MP4 conversion...
[in#0 @ 0x6309c1856740] Error opening input: Invalid data found when processing input
Error opening input file test_small.ts.
Error opening input files: Invalid data found when processing input
❌ MP4 conversion failed with exit code: 183
📋 Common causes:
   - Empty or corrupted input file
   - Invalid RTSP stream format
   - Network interruption during capture
💡 Try running: ./cleanup_empty_streams.sh to remove problematic files
