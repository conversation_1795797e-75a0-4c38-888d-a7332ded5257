🎬 Converting .ts to .mp4 format and uploading to S3...
📁 Input: /home/<USER>/Documents/augment-projects/zmt-live/encoded/stream/stream_20250708_155454_zmt.ts
☁️ S3 Bucket: compressed-streams
📊 Input file size: 5164548 bytes
✅ Bucket exists: compressed-streams
📁 Output: mp4/compressed.mp4 (compressed stream)
Input #0, mpegts, from '/home/<USER>/Documents/augment-projects/zmt-live/encoded/stream/stream_20250708_155454_zmt.ts':
  Duration: 00:02:59.80, start: 1.600000, bitrate: 229 kb/s
  Program 1 
    Metadata:
      service_name    : Stream
      service_provider: FFmpeg
  Stream #0:0[0x100]: Video: h264 (High) ([27][0][0][0] / 0x001B), yuvj420p(pc, bt709, progressive), 1024x768 [SAR 128:129 DAR 512:387], 30 fps, 30 tbr, 90k tbn
Output #0, mp4, to 'mp4/compressed.mp4':
  Metadata:
    encoder         : Lavf60.16.100
  Stream #0:0: Video: h264 (High) (avc1 / 0x31637661), yuvj420p(pc, bt709, progressive), 1024x768 [SAR 128:129 DAR 512:387], q=2-31, 30 fps, 30 tbr, 90k tbn
Stream mapping:
  Stream #0:0 -> #0:0 (copy)
Press [q] to stop, [?] for help
size=       0kB time=-00:00:00.06 bitrate=  -0.0kbits/s speed=N/A    
[out#0/mp4 @ 0x63b9ade2fb80] video:3649kB audio:0kB subtitle:0kB other streams:0kB global headers:0kB muxing overhead: 1.542083%
size=    3705kB time=00:02:59.70 bitrate= 168.9kbits/s speed=6.27e+03x    
✅ MP4 conversion completed
⬆️ Uploading original .ts file to S3...
📤 S3 destination: s3://compressed-streams/2025/07/08/stream_20250708_155454_zmt.ts
✅ .ts file uploaded successfully
⬆️ Uploading MP4 file to S3...
📤 S3 destination: s3://compressed-streams/2025/07/08/compressed.mp4
✅ MP4 file uploaded successfully
🔗 S3 Pre-signed URL (24h): https://compressed-streams.s3.us-east-1.amazonaws.com/2025/07/08/compressed.mp4?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIARWTHWVS7NV2YMDHW%2F20250708%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250708T195819Z&X-Amz-Expires=86400&X-Amz-SignedHeaders=host&X-Amz-Signature=41868f822389f0566ade80e11df6aeb67fa64fea1ca1696fe55e1d7079a00f9f
💾 Keeping MP4 file locally for web serving...
✅ MP4 file ready for local serving and S3 access

🎯 Local MP4 Video URL:
/mp4/compressed.mp4

☁️ S3 MP4 Path:
s3://compressed-streams/2025/07/08/compressed.mp4

🔗 S3 Pre-signed URL (24h):
https://compressed-streams.s3.us-east-1.amazonaws.com/2025/07/08/compressed.mp4?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIARWTHWVS7NV2YMDHW%2F20250708%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250708T195819Z&X-Amz-Expires=86400&X-Amz-SignedHeaders=host&X-Amz-Signature=41868f822389f0566ade80e11df6aeb67fa64fea1ca1696fe55e1d7079a00f9f

📋 Use local URL for immediate access, S3 URL for external sharing
📱 Web dashboard integration: mp4/20250708_155454/compressed_url.txt
🎉 Process completed! MP4 video ready for local web serving and S3 access.
