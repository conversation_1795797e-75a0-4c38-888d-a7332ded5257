🎬 Converting .ts to .mp4 format and uploading to S3...
📁 Input: /home/<USER>/Documents/augment-projects/zmt-live/original/stream/stream_20250626_121844.ts
☁️ S3 Bucket: original-streams
📊 Input file size: 0 bytes
✅ Bucket exists: original-streams
📁 Output: mp4/original.mp4 (original stream)
[in#0 @ 0x5a5fd1cb4740] Error opening input: Invalid data found when processing input
Error opening input file /home/<USER>/Documents/augment-projects/zmt-live/original/stream/stream_20250626_121844.ts.
Error opening input files: Invalid data found when processing input
❌ MP4 conversion failed. Check log: logs/mp4_conversion_20250626_140735.log
