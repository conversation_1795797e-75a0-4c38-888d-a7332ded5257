🎬 Converting .ts to .mp4 format and uploading to S3...
📁 Input: /home/<USER>/Documents/augment-projects/zmt-live/original/stream/stream_20250707_152717.ts
☁️ S3 Bucket: original-streams
📊 Input file size: 8406232 bytes
✅ Bucket exists: original-streams
📁 Output: mp4/original.mp4 (original stream)
Input #0, mpegts, from '/home/<USER>/Documents/augment-projects/zmt-live/original/stream/stream_20250707_152717.ts':
  Duration: 00:00:59.17, start: 2.259278, bitrate: 1136 kb/s
  Program 1 
    Metadata:
      service_name    : Stream
      service_provider: FFmpeg
  Stream #0:0[0x100]: Video: h264 (High) ([27][0][0][0] / 0x001B), yuvj420p(pc, bt709, progressive), 1024x768 [SAR 128:129 DAR 512:387], 30 fps, 30 tbr, 90k tbn
Output #0, mp4, to 'mp4/original.mp4':
  Metadata:
    encoder         : Lavf60.16.100
  Stream #0:0: Video: h264 (High) (avc1 / 0x31637661), yuvj420p(pc, bt709, progressive), 1024x768 [SAR 128:129 DAR 512:387], q=2-31, 30 fps, 30 tbr, 90k tbn
Stream mapping:
  Stream #0:0 -> #0:0 (copy)
Press [q] to stop, [?] for help
size=       0kB time=00:00:00.00 bitrate=N/A speed=N/A    
[out#0/mp4 @ 0x5664490c6380] video:7611kB audio:0kB subtitle:0kB other streams:0kB global headers:0kB muxing overhead: 0.229181%
size=    7629kB time=00:00:59.13 bitrate=1056.8kbits/s speed=2.69e+03x    
✅ MP4 conversion completed
⬆️ Uploading original .ts file to S3...
📤 S3 destination: s3://original-streams/2025/07/07/stream_20250707_152717.ts
✅ .ts file uploaded successfully
⬆️ Uploading MP4 file to S3...
📤 S3 destination: s3://original-streams/2025/07/07/original.mp4
✅ MP4 file uploaded successfully
🔗 S3 Pre-signed URL (24h): https://original-streams.s3.us-east-1.amazonaws.com/2025/07/07/original.mp4?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIARWTHWVS7NV2YMDHW%2F20250707%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250707T192849Z&X-Amz-Expires=86400&X-Amz-SignedHeaders=host&X-Amz-Signature=738be1cf45b0b07b17f11f76b84120145559472bc28083f6be4f6cbd12778cf5
💾 Keeping MP4 file locally for web serving...
✅ MP4 file ready for local serving and S3 access

🎯 Local MP4 Video URL:
/mp4/original.mp4

☁️ S3 MP4 Path:
s3://original-streams/2025/07/07/original.mp4

🔗 S3 Pre-signed URL (24h):
https://original-streams.s3.us-east-1.amazonaws.com/2025/07/07/original.mp4?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIARWTHWVS7NV2YMDHW%2F20250707%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250707T192849Z&X-Amz-Expires=86400&X-Amz-SignedHeaders=host&X-Amz-Signature=738be1cf45b0b07b17f11f76b84120145559472bc28083f6be4f6cbd12778cf5

📋 Use local URL for immediate access, S3 URL for external sharing
📱 Web dashboard integration: mp4/20250707_152717/original_url.txt
🎉 Process completed! MP4 video ready for local web serving and S3 access.
