🎬 Converting .ts to .mp4 format and uploading to S3...
📁 Input: /home/<USER>/Documents/augment-projects/zmt-live/original/stream/stream_20250707_153637.ts
☁️ S3 Bucket: original-streams
📊 Input file size: 8422964 bytes
✅ Bucket exists: original-streams
📁 Output: mp4/original.mp4 (original stream)
Input #0, mpegts, from '/home/<USER>/Documents/augment-projects/zmt-live/original/stream/stream_20250707_153637.ts':
  Duration: 00:00:59.42, start: 2.000000, bitrate: 1133 kb/s
  Program 1 
    Metadata:
      service_name    : Stream
      service_provider: FFmpeg
  Stream #0:0[0x100]: Video: h264 (High) ([27][0][0][0] / 0x001B), yuvj420p(pc, bt709, progressive), 1024x768 [SAR 128:129 DAR 512:387], 30 fps, 30 tbr, 90k tbn
Output #0, mp4, to 'mp4/original.mp4':
  Metadata:
    encoder         : Lavf60.16.100
  Stream #0:0: Video: h264 (High) (avc1 / 0x31637661), yuvj420p(pc, bt709, progressive), 1024x768 [SAR 128:129 DAR 512:387], q=2-31, 30 fps, 30 tbr, 90k tbn
Stream mapping:
  Stream #0:0 -> #0:0 (copy)
Press [q] to stop, [?] for help
size=       0kB time=00:00:00.00 bitrate=N/A speed=N/A    
[out#0/mp4 @ 0x5afa201459c0] video:7627kB audio:0kB subtitle:0kB other streams:0kB global headers:0kB muxing overhead: 0.229429%
size=    7644kB time=00:00:59.39 bitrate=1054.4kbits/s speed=3e+03x    
✅ MP4 conversion completed
⬆️ Uploading original .ts file to S3...
📤 S3 destination: s3://original-streams/2025/07/07/stream_20250707_153637.ts
✅ .ts file uploaded successfully
⬆️ Uploading MP4 file to S3...
📤 S3 destination: s3://original-streams/2025/07/07/original.mp4
✅ MP4 file uploaded successfully
🔗 S3 Pre-signed URL (24h): https://original-streams.s3.us-east-1.amazonaws.com/2025/07/07/original.mp4?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIARWTHWVS7NV2YMDHW%2F20250707%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250707T193749Z&X-Amz-Expires=86400&X-Amz-SignedHeaders=host&X-Amz-Signature=3c1b6f372fdff5746733fd082576f86a075ae486c59d2df282c9ee7d2eee5191
💾 Keeping MP4 file locally for web serving...
✅ MP4 file ready for local serving and S3 access

🎯 Local MP4 Video URL:
/mp4/original.mp4

☁️ S3 MP4 Path:
s3://original-streams/2025/07/07/original.mp4

🔗 S3 Pre-signed URL (24h):
https://original-streams.s3.us-east-1.amazonaws.com/2025/07/07/original.mp4?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIARWTHWVS7NV2YMDHW%2F20250707%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250707T193749Z&X-Amz-Expires=86400&X-Amz-SignedHeaders=host&X-Amz-Signature=3c1b6f372fdff5746733fd082576f86a075ae486c59d2df282c9ee7d2eee5191

📋 Use local URL for immediate access, S3 URL for external sharing
📱 Web dashboard integration: mp4/20250707_153637/original_url.txt
🎉 Process completed! MP4 video ready for local web serving and S3 access.
