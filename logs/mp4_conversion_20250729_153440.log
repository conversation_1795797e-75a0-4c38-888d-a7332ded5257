🎬 Converting .ts to .mp4 format and uploading to S3...
📁 Input: /home/<USER>/Documents/augment-projects/zmt-live/original/stream/stream_20250729_153331.ts
☁️ S3 Bucket: original-streams
📊 Input file size: 8371828 bytes
✅ Bucket exists: original-streams
📁 Output: mp4/original.mp4 (original stream)
📊 Input file size: 8371828 bytes
🎬 Starting MP4 conversion...
Input #0, mpegts, from '/home/<USER>/Documents/augment-projects/zmt-live/original/stream/stream_20250729_153331.ts':
  Duration: 00:00:59.07, start: 2.333000, bitrate: 1133 kb/s
  Program 1 
    Metadata:
      service_name    : Stream
      service_provider: FFmpeg
  Stream #0:0[0x100]: Video: h264 (High) ([27][0][0][0] / 0x001B), yuvj420p(pc, bt709, progressive), 1024x768 [SAR 128:129 DAR 512:387], 30 fps, 30 tbr, 90k tbn
Output #0, mp4, to 'mp4/original.mp4':
  Metadata:
    encoder         : Lavf60.16.100
  Stream #0:0: Video: h264 (High) (avc1 / 0x31637661), yuvj420p(pc, bt709, progressive), 1024x768 [SAR 128:129 DAR 512:387], q=2-31, 30 fps, 30 tbr, 90k tbn
Stream mapping:
  Stream #0:0 -> #0:0 (copy)
Press [q] to stop, [?] for help
size=       0kB time=00:00:00.00 bitrate=N/A speed=N/A    
[out#0/mp4 @ 0x5d91245a7200] video:7578kB audio:0kB subtitle:0kB other streams:0kB global headers:0kB muxing overhead: 0.229659%
size=    7596kB time=00:00:59.03 bitrate=1054.0kbits/s speed=3.64e+03x    
✅ MP4 conversion completed successfully
📊 Output file size: 7.5M (7778012 bytes)
⬆️ Uploading original .ts file to S3...
📤 S3 destination: s3://original-streams/2025/07/29/stream_20250729_153331.ts
